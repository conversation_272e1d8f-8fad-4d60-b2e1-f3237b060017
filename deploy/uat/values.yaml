containerPort: 80

service:
  type: NodePort

resources:
  requests:
    cpu: 50m
    memory: 256Mi

affinity:
  podAntiAffinity:
    enabled: true
  nodeAffinity:
    enabled: true
    key: "Environment"
    values:
      - uat

ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig":
      { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-2:764067299928:certificate/a5a60cc2-aeb0-4a0f-9ff5-7bd93175f4a6
    alb.ingress.kubernetes.io/group.name: tripudiotech-uat
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/tags: Environment=Tripudiotech-UAT
    kubernetes.io/ingress.class: alb
  paths:
    - /*
  hosts:
    - ui-uat.glideyoke.com
    - ui-qa-uat.glideyoke.com
    - ui-po-uat.glideyoke.com
    - deploy-uat.glideyoke.com

variables:
  AUTH_SERVICE_URL: https://auth-uat.glideyoke.com
  COOKIE_DOMAIN: .glideyoke.com
  DEFAULT_TENANT_ID: master
  IDENTITY_SERVICE_URL: https://identity-uat.glideyoke.com
  MASTER_DOMAIN: ui-uat