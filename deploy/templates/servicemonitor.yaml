{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "ui-user.fullname" . }}
  labels:
    {{- include "ui-user.labels" . | nindent 4 }}
spec:
  jobLabel: {{ include "ui-user.fullname" . }}
  endpoints:
    - port: http
      path: {{ .Values.serviceMonitor.path }}
      interval: {{ .Values.serviceMonitor.interval }}
  selector:
    matchLabels: 
      {{- include "ui-user.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}      
{{- end }}