/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { ClassificationDetail, classificationUrls, Classification, fetch, schemaUrls } from '@glidesystems/api';
import { create } from 'zustand';

type ClassificationDetailStore = {
    isLoading: Record<string, boolean>;
    classification: Record<string, ClassificationDetail>;
    schemaClassification: Record<string, Classification[]>;
    getClassification: (name: string) => Promise<ClassificationDetail>;
    getClassifications: (classifications: string[]) => Promise<ClassificationDetail[]>;
    getSchemaClassification: (entityType: string) => Promise<Classification[]>;
};

const useClassificationDetail = create<ClassificationDetailStore>((set, get) => ({
    schemaClassification: {},
    isLoading: {},
    classification: {},
    getClassification: async (name: string) => {
        const cachedClassification = get().classification[name];
        if (cachedClassification) {
            return cachedClassification;
        }
        try {
            set({ isLoading: { ...get().isLoading, [name]: true } });
            const { data } = await fetch({
                ...classificationUrls.getClassificationDetail,
                params: { name },
            });
            set({
                classification: {
                    ...get().classification,
                    [name]: data,
                },
            });
            return data;
        } finally {
            set({ isLoading: { ...get().isLoading, [name]: false } });
        }
    },
    getClassifications: async (classifications: string[]) => {
        const results: (any | null)[] = [];
        const batchSize = 10;

        for (let i = 0; i < classifications.length; i += batchSize) {
            const batch = classifications.slice(i, i + batchSize);
            const batchResults = await Promise.all(batch.map((name) => get().getClassification(name)));
            results.push(...batchResults);
        }

        return results;
    },
    getSchemaClassification: async (entityType: string) => {
        try {
            if (get().schemaClassification[entityType]) return get().schemaClassification[entityType];

            const { data } = await fetch({
                ...schemaUrls.getSchemaClassification,
                params: { entityType },
                qs: {
                    fields: 'permissions',
                },
            });
            set({
                schemaClassification: {
                    ...get().schemaClassification,
                    [entityType]: data,
                },
            });
            return data;
        } catch (err) {
        } finally {
        }
    },
}));

export default useClassificationDetail;
