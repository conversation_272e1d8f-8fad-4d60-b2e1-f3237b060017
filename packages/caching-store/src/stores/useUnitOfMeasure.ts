/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import isEmpty from 'lodash/isEmpty';
import sortBy from 'lodash/sortBy';
import _get from 'lodash/get';
import { persist } from 'zustand/middleware';
import { schemaUrls, fetch as apiF<PERSON>ch, Schema } from '@glidesystems/api';
import { GET_UNIT_LIMIT_RANGES } from '@glidesystems/styleguide';
import { PersistStorage } from '../persist';
import { GetExpriedDate, IsExpired } from './useEntityMetadata';

interface UnitOfMeasureState {
    quantityKind?: Record<string, any>;
    quantityUnit?: Record<string, any>;
    expiredAt?: number;
    updateQuantityUnit?: (key: string, value: string) => void;
    setQuantityKind?: (quantityKind: any[]) => void;
    getQuantityKind?: () => Promise<void>;
    getQuantityUnit?: (groupId: any) => Promise<void>;
    getUnitOfMeasureFromSchema?: (detailSchema: Schema) => void;
}

const MAX_STORED_DATE = 30;

const convertArrayObjectToMap = (data: any[], keyName: string = 'uri'): Record<string, any> => {
    if (isEmpty(data) || !keyName) return {};

    return data.reduce((result: Record<string, any>, d: any) => ({ ...result, [d[keyName]]: d }), {});
};

const useUnitOfMeasure = create(
    persist<UnitOfMeasureState>(
        (set, get) => ({
            quantityKind: {},
            quantityUnit: {},
            expiredAt: 0,
            setQuantityKind: (quantityKind: any[]) => set({ quantityKind }),
            updateQuantityUnit: (kindId: string, units: string) =>
                set((state) => ({
                    ...state,
                    quantityUnit: {
                        ...state.quantityUnit,
                        [kindId]: units,
                    },
                })),
            getQuantityKind: async () => {
                apiFetch({
                    ...schemaUrls.getQuantityGroups,
                    url: `${schemaUrls.getQuantityGroups.url}?limit=${GET_UNIT_LIMIT_RANGES}`,
                }).then((res) => {
                    const {
                        data: { data },
                    } = res;
                    set({
                        quantityKind: convertArrayObjectToMap(data),
                        expiredAt: GetExpriedDate(MAX_STORED_DATE),
                    });
                });
            },
            getQuantityUnit: async (groupId) => {
                apiFetch({
                    ...schemaUrls.getQuantityUnits,
                    url: `${schemaUrls.getQuantityUnits.url}?limit=${GET_UNIT_LIMIT_RANGES}`,
                    params: {
                        groupId: encodeURIComponent(groupId),
                    },
                })
                    .then((res) => {
                        const {
                            data: { data },
                        } = res;
                        set((state) => ({
                            quantityUnit: {
                                ...state.quantityUnit,
                                [groupId]: convertArrayObjectToMap(sortBy(data, ['label'])),
                            },
                        }));
                    })
                    .catch((e) => {
                        // Log the error message
                        console.warn(e?.response?.message || e.message);
                    });
            },
            getUnitOfMeasureFromSchema: (detailSchema: Schema) => {
                const { attributes } = detailSchema;
                const quantityUnitList = [];

                Object.entries(attributes).forEach(([entryKey, entryValue]) => {
                    const unitOfMeasureAttr = _get(entryValue, 'unitOfMeasure', null);
                    if (!isEmpty(unitOfMeasureAttr)) {
                        quantityUnitList.push(unitOfMeasureAttr);
                    }
                });

                const { quantityKind, quantityUnit, getQuantityKind, getQuantityUnit } = get();

                if (!isEmpty(quantityUnitList)) {
                    if (isEmpty(quantityKind)) {
                        getQuantityKind();
                    }

                    quantityUnitList.forEach((item) => {
                        if (isEmpty(_get(quantityUnit, `${item.quantityKind}`, {}))) {
                            getQuantityUnit(item.quantityKind);
                        }
                    });
                }
            },
        }),
        {
            name: 'unit-of-measure-data',
            getStorage: () => PersistStorage,
            partialize: ({ quantityKind, quantityUnit, expiredAt }) => ({
                quantityKind,
                quantityUnit,
                expiredAt,
            }),
            onRehydrateStorage: (state) => {
                return (state, error) => {
                    if (error) return;
                    if (IsExpired(state.expiredAt)) {
                        useUnitOfMeasure.persist.clearStorage();
                    }
                };
            },
            version: 1,
            migrate: (persistedState: any, version) => {
                if (version === 1) {
                    // Reset the old stored data
                    persistedState = {
                        ...persistedState,
                        quantityKind: {},
                        quantityUnit: {},
                        expiredAt: 0,
                    };
                }

                return persistedState;
            },
        }
    )
);

export default useUnitOfMeasure;
