/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { entityUrls, fetch as apiFetch, buildExactQuery } from '@glidesystems/api';
import { persist } from 'zustand/middleware';
import { PersistStorage } from '../persist';

type Metadata = {
    id: string;
    name: string;
    dataType: string;
    revision?: string;
    email?: string;
};

type EntityMetadataStore = {
    entities: Record<string, any> | null;
    entitiesByProperty: Record<string, any> | null;
    entityAccess: Record<string, any> | null;
    expiredAt: number;
    getEntity: (id, dateType) => Promise<Metadata>;
    getEntityAccess: (id: string, force?: boolean) => any;
    getEntityByProperty: (entityType: string, property: string, value: string) => Promise<Metadata>;
};

const MAX_STORED_DATE = 14;

const useEntityMetadata = create(
    persist<EntityMetadataStore>(
        (set, get) => ({
            entities: {},
            entityAccess: {},
            expiredAt: 0,
            entitiesByProperty: {},
            getEntityAccess: async (id: string, force = false) => {
                if (get().entityAccess[id] && !force) {
                    return get().entityAccess[id];
                }
                try {
                    const {
                        data: { data },
                    } = await apiFetch({
                        ...entityUrls.getEntityAccesses,
                        params: {
                            entityId: id,
                        },
                    });
                    set({
                        entityAccess: {
                            ...get().entityAccess,
                            [id]: [...data],
                        },
                    });
                    return data;
                } catch (e) {
                    return [];
                } finally {
                }
            },
            getEntityByProperty: async (entityType: string, property: string, value: string) => {
                const key = [entityType, property, value].join();

                if (get().entitiesByProperty[key]) return get().entitiesByProperty[key];

                try {
                    const { data } = await apiFetch({
                        ...entityUrls.getListEntity,
                        params: { entityType },
                        qs: {
                            query: JSON.stringify(buildExactQuery(property, value)),
                            limit: 1,
                        },
                        skipToast: true,
                    });
                    const {
                        id,
                        properties: { name, email },
                    } = data.data[0];
                    const entity = {
                        id,
                        name,
                        email,
                    };
                    set({
                        entitiesByProperty: {
                            ...get().entitiesByProperty,
                            [key]: entity,
                        },
                    });
                    if (!get().expiredAt) {
                        set({ expiredAt: GetExpriedDate(MAX_STORED_DATE) });
                    }
                    return entity;
                } catch (err) {
                    return null;
                }
            },
            getEntity: async (id, dataType) => {
                const entityKey = [id, dataType].join();
                if (get().entities[entityKey]) return get().entities[entityKey];

                try {
                    const {
                        data: {
                            properties: { name, revision, email },
                        },
                    } = await apiFetch({
                        ...entityUrls.getEntityById,
                        params: {
                            entityId: id,
                            entityType: dataType,
                        },
                        skipToast: true,
                    });

                    const entity = {
                        id,
                        dataType,
                        revision,
                        name: name,
                        email,
                    };
                    set({
                        entities: {
                            ...get().entities,
                            [entityKey]: entity,
                        },
                    });

                    if (!get().expiredAt) {
                        set({ expiredAt: GetExpriedDate(MAX_STORED_DATE) });
                    }
                    return entity;
                } catch {
                    return null;
                }
            },
        }),
        {
            name: 'entity-metadata',
            getStorage: () => PersistStorage,
            onRehydrateStorage: (state) => {
                return (state, error) => {
                    if (error) {
                        return;
                    }
                    if (IsExpired(state.expiredAt)) {
                        useEntityMetadata.persist.clearStorage();
                    }
                };
            },
        }
    )
);

export const IsExpired = (date: number): boolean => {
    return date < Date.now().valueOf();
};

export const GetExpriedDate = (NoOfDays): number => {
    const expiredDate = new Date();
    expiredDate.setDate(expiredDate.getDate() + NoOfDays);
    return expiredDate.valueOf();
};

export default useEntityMetadata;
