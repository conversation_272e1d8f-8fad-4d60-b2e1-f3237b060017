/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { authenticationService } from '@glidesystems/api';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { PersistStorage } from '../persist';

export type GlobalConfigItem = {
    key: string;
    value: string;
};

export type GlobalConfigStore = {
    isLoaded: boolean;
    configs: GlobalConfigItem[];
    fetchGlobalConfigs: () => Promise<void>;
    getDateTimeFormat: () => string;
    getDateFormat: () => string;
    getWorkspaceFeature: () => string;
};
const DEFAULT_DATETIME_FORMAT = 'yyyy-MM-DD HH:mm:ss';
const DEFAULT_DATE_FORMAT = 'yyyy-MM-DD';

const SYSTEM_CONFIG = {
    DATETIME_FORMAT: 'formatting.datetime',
    DATE_FORMAT: 'formatting.date',
    WORKSPACE_FEATURE: 'system.feature.workspace',
};

const useGlobalConfig = create(
    persist<GlobalConfigStore>(
        (set, get) => ({
            isLoaded: false,
            configs: [],
            fetchGlobalConfigs: async (force = false) => {
                if (get().isLoaded && !force) return;
                const { data: configs } = await authenticationService.getGlobalConfigs();
                set({ configs, isLoaded: true });
            },
            getDateTimeFormat: () => {
                return (
                    get().configs.find((item) => item.key === SYSTEM_CONFIG.DATETIME_FORMAT)?.value ||
                    DEFAULT_DATETIME_FORMAT
                );
            },
            getDateFormat: () => {
                return (
                    get().configs.find((item) => item.key === SYSTEM_CONFIG.DATE_FORMAT)?.value || DEFAULT_DATE_FORMAT
                );
            },
            getWorkspaceFeature: () => {
                return get().configs.find((item) => item.key === SYSTEM_CONFIG.WORKSPACE_FEATURE)?.value;
            },
        }),
        {
            name: 'global-config',
            getStorage: () => PersistStorage,
        }
    )
);

export default useGlobalConfig;
