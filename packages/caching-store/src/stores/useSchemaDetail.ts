/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, Lifecycle, Schema, schemaUrls } from '@glidesystems/api';

export interface LifecycleWithState {
    lifeCycle: Lifecycle;
    states: Record<string, any>;
}

export interface SchemaWithLifeCycleDetail extends Schema {
    lifecycles: LifecycleWithState[];
}

type SchemaDetailStore = {
    schema: Record<string, SchemaWithLifeCycleDetail> | null;
    isLoading: Record<string, boolean>;
    isLoaded: boolean;
    getSchema: (entityTypeName, withLifecycle?: boolean) => Promise<SchemaWithLifeCycleDetail>;
    getMultipleSchema: (entityTypeNames) => Promise<SchemaWithLifeCycleDetail[]>;
    getAttributeName: (schemaDetail: SchemaWithLifeCycleDetail, attributeId: string) => string;
};

const useSchemaDetail = create<SchemaDetailStore>((set, get) => ({
    schema: {},
    isLoading: {},
    isLoaded: true,
    getSchema: async (entityTypeName, withLifecycle = false): Promise<SchemaWithLifeCycleDetail> => {
        const type = get().schema[entityTypeName];
        if (type && ((withLifecycle && type?.lifecycles) || !withLifecycle)) return get().schema[entityTypeName];

        set({ isLoading: { ...get().isLoading, [entityTypeName]: true }, isLoaded: false });
        try {
            let fetchs = [
                fetch({
                    ...schemaUrls.getSchemaDetail,
                    params: { entityTypeName },
                }),
            ];
            if (withLifecycle) {
                fetchs.push(
                    fetch({
                        ...schemaUrls.fetchEntityLifecycle,
                        params: {
                            entityTypeName,
                        },
                    })
                );
            }
            const results = await Promise.all(fetchs);
            const data: SchemaWithLifeCycleDetail = withLifecycle
                ? { ...results[0].data, lifecycles: results[1].data.data }
                : results[0].data;
            set({
                schema: {
                    ...get().schema,
                    [entityTypeName]: data,
                },
            });
            return data;
        } finally {
            set({ isLoading: { ...get().isLoading, [entityTypeName]: false } });
            const pendingReqs = Object.values(get().isLoading).filter((loading) => loading);
            if (pendingReqs.length <= 1) {
                set({ isLoaded: true });
            }
        }
    },
    getMultipleSchema: async (types) => {
        const requests = [];
        types.forEach((type) => requests.push(get().getSchema(type)));
        return await Promise.all(requests);
    },
    getAttributeName: (schemaDetail: SchemaWithLifeCycleDetail, attributeId: string) => {
        if (!schemaDetail || !schemaDetail.attributes) {
            return '';
        }
        const attribute = Object.values(schemaDetail.attributes).find((attr) => attr.id === attributeId);
        return attribute ? attribute.name : '';
    },
}));

export default useSchemaDetail;
