/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, DEFAULT_CLIENT_SIDE_LIMIT, entityUrls } from '@glidesystems/api';

export interface BookmarkPayload {
    name: string;
    folderPath: string;
    entityId: string;
    relationId?: string;
}

type BookmarkStore = {
    loaded: boolean;
    bookmarks: Array<Record<string, any>>;
    getBookmarks: (userId: string, force?: boolean) => Promise<Array<Record<string, any>>>;
    doesBookmarkExistForEntity: (userId: string, entityId: string) => Promise<boolean>;
    bookmarkEntity: (userId: string, payload: BookmarkPayload, isEdit?: boolean) => Promise<void>;
    unbookmarkEntity: (userId: string, entityId: string) => Promise<void>;
    batchBookmarkUpdate: (userId: string, payload: BookmarkPayload[]) => Promise<void>;
};

const BOOKMARK_RELATION = 'BOOKMARK';

const useBookmarks = create<BookmarkStore>((set, get) => ({
    loaded: false,
    bookmarks: [],
    getBookmarks: async (userId: string, force = false) => {
        if (get().loaded && !force) return get().bookmarks;
        set({ ...get(), loaded: false });
        const bookmarkResponse = await fetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: {
                entityId: userId,
            },
            qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT, offset: 0, relationNames: BOOKMARK_RELATION },
        });
        const bookmarksWithProperties: Array<Record<string, any>> = bookmarkResponse.data.data;
        set({
            loaded: true,
            bookmarks: bookmarksWithProperties,
        });
        return bookmarksWithProperties;
    },
    doesBookmarkExistForEntity: async (userId: string, entityId: string) => {
        if (get().loaded) {
            return get().bookmarks.filter((bookmark) => bookmark.id === entityId)?.length > 0;
        }
        await get().getBookmarks(userId);
        return get().bookmarks.filter((bookmark) => bookmark.id === entityId)?.length > 0;
    },
    bookmarkEntity: async (userId: string, payload: BookmarkPayload, isEdit: boolean = false) => {
        if (userId === undefined) return;
        if (isEdit) {
            if (payload.relationId !== undefined) {
                await fetch({
                    ...entityUrls.updateRelation,
                    params: {
                        fromEntityId: userId,
                        relationType: BOOKMARK_RELATION,
                        relationId: payload.relationId,
                    },
                    data: {
                        properties: {
                            name: payload.name,
                            folderPath: payload.folderPath,
                        },
                    },
                    errorMessage: 'Error updating bookmark',
                    successMessage: 'Successfully updated bookmark',
                });
            } else {
                console.error('Missing relation id');
            }
        } else {
            if (payload.entityId !== undefined) {
                await fetch({
                    ...entityUrls.createRelation,
                    params: {
                        fromEntityId: userId,
                        relationType: BOOKMARK_RELATION,
                        toEntityId: payload.entityId,
                    },
                    data: {
                        properties: {
                            name: payload.name,
                            folderPath: payload.folderPath,
                        },
                    },
                    errorMessage: 'Error creating bookmark',
                    successMessage: 'Successfully created bookmark',
                });
            }
        }
    },
    unbookmarkEntity: async (userId: string, entityId: string) => {
        if (userId !== undefined && entityId !== undefined) {
            const res = await fetch({
                ...entityUrls.deleteRelation,
                params: {
                    fromEntityId: userId,
                    relationType: BOOKMARK_RELATION,
                    toEntityId: entityId,
                },
                errorMessage: 'Error removing bookmark',
                successMessage: 'Successfully removed bookmark',
            });
        }
    },
    batchBookmarkUpdate: async (userId: string, payloads: BookmarkPayload[]) => {
        set({ ...get(), loaded: false });
        await Promise.all(
            payloads.map(async (payload) => {
                await get().bookmarkEntity(userId, payload, true);
            })
        );
        await get().getBookmarks(userId, true);
        set({ ...get(), loaded: true });
    },
}));

export default useBookmarks;
