type AgentStore = {
    loaded: boolean;
    teams: Array<Record<string, any>>;
    companies: Array<Record<string, any>>;
    departments: Array<Record<string, any>>;
    people: Array<Record<string, any>>;
    userCompany: Record<string, any>;
    setData: (data: AgentStore | Partial<AgentStore>) => void;
    getAgents: (userId: string) => any;
    getUserCompany: (userId: string) => any;
};
declare const useAgent: import("zustand").UseBoundStore<import("zustand").StoreApi<AgentStore>>;
export default useAgent;
//# sourceMappingURL=useAgent.d.ts.map