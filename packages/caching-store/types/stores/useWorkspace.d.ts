import { Workspace } from '@glidesystems/api';
export interface WorkspaceStore {
    isLoaded: boolean;
    isLoading: boolean;
    workspaces: Workspace[];
    selectedWorkspace: Workspace | null;
    fetchWorkspaces: () => Promise<void>;
    changeWorkspace: (workspace: Workspace) => Promise<void>;
    removeWorkspace: () => Promise<void>;
    getByName: (name: string) => Workspace | undefined;
}
/**
 * Clear workspace-specific cached data from stores
 */
export declare function clearCachingStores(): Promise<void>;
export declare function remountAllMountedApps(): Promise<void>;
declare const useWorkspace: import("zustand").UseBoundStore<import("zustand").StoreApi<WorkspaceStore>>;
export default useWorkspace;
//# sourceMappingURL=useWorkspace.d.ts.map