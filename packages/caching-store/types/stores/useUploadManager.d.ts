type Item = {
    id: string;
    name: string;
    file?: File;
    folderId?: string;
    error?: boolean;
    errorType?: number | null;
    progress?: number | null;
    abortController?: any;
    extension?: string;
    callback?: () => void;
};
interface UploadManagerStore {
    items: string[];
    expanded: boolean;
    uploadFiles: (folderId: string, files: File[], callback?: () => void) => Promise<void>;
    uploadFile: (folderId: string, files: File, callback?: () => void) => Promise<void>;
    itemProgress: Record<string, Item>;
    replace: (requestId: string) => void;
    toggleExpanded: () => void;
    cancelAll: (force?: boolean) => boolean;
    cancel: (requestId: string) => Promise<void>;
    createEntitiesBatch: (entityId: string, relationType: string, entityType: string, bodies: any, callback?: () => void) => Promise<void>;
}
declare const useUploadManager: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<UploadManagerStore>, "subscribe"> & {
    subscribe: {
        (listener: (selectedState: UploadManagerStore, previousSelectedState: UploadManagerStore) => void): () => void;
        <U>(selector: (state: UploadManagerStore) => U, listener: (selectedState: U, previousSelectedState: U) => void, options?: {
            equalityFn?: (a: U, b: U) => boolean;
            fireImmediately?: boolean;
        }): () => void;
    };
}>;
export default useUploadManager;
//# sourceMappingURL=useUploadManager.d.ts.map