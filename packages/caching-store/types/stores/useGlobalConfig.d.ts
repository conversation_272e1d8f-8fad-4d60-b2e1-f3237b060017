export type GlobalConfigItem = {
    key: string;
    value: string;
};
export type GlobalConfigStore = {
    isLoaded: boolean;
    configs: GlobalConfigItem[];
    fetchGlobalConfigs: () => Promise<void>;
    getDateTimeFormat: () => string;
    getDateFormat: () => string;
    getWorkspaceFeature: () => string;
};
declare const useGlobalConfig: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<GlobalConfigStore>, "persist"> & {
    persist: {
        setOptions: (options: Partial<import("zustand/middleware").PersistOptions<GlobalConfigStore, GlobalConfigStore>>) => void;
        clearStorage: () => void;
        rehydrate: () => Promise<void> | void;
        hasHydrated: () => boolean;
        onHydrate: (fn: (state: GlobalConfigStore) => void) => () => void;
        onFinishHydration: (fn: (state: GlobalConfigStore) => void) => () => void;
        getOptions: () => Partial<import("zustand/middleware").PersistOptions<GlobalConfigStore, GlobalConfigStore>>;
    };
}>;
export default useGlobalConfig;
//# sourceMappingURL=useGlobalConfig.d.ts.map