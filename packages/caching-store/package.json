{"name": "@glidesystems/caching-store", "version": "1.0.0", "scripts": {"start": "concurrently pnpm:serve pnpm:emit-types", "precommit": "lint-staged", "serve": "rspack serve --port 9015", "start:standalone": "concurrently pnpm:emit-types rspack serve --env standalone", "emit-types": "tsc --watch --emitDeclarationOnly", "build": "concurrently pnpm:build:*", "build:rspack": "rspack --mode=production", "analyze": "rspack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "build:types": "tsc", "clean": "rm -rf node_modules"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@rspack/core": "^1.2.3", "@rspack/cli": "^1.2.3", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "typescript": "5.7.3", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-merge": "^5.8.0", "lodash": "^4.17.21", "zustand": "^4.0.0", "@types/uuid": "^8.3.2"}, "dependencies": {"@types/jest": "^27.0.1", "@types/systemjs": "^6.1.1", "idb-keyval": "^6.2.0", "react": "^18.3.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "uuid": "^8.3.2", "@glidesystems/api": "workspace:*", "@glidesystems/styleguide": "workspace:*"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --no-ignore --fix", "git add --force"], "*.{json,md,js,jsx,ts,tsx}": ["prettier --write", "git add --force"]}, "types": "types/glidesystems-caching-store.d.ts"}