/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { Suspense, useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { LoadingOverlay, PlusIcon, NotificationIcon, BookmarkOutlineIcon } from '@glidesystems/styleguide';
import { Link, useNavigate } from 'react-router-dom';
import CreatePalette from './components/CreatePaletteV2/CreatePalette';
import { AppBar, Divider, IconButton, Popper, styled, Toolbar, useMediaQuery, useTheme } from '@mui/material';
import { CollapseIcon } from './components/icons/Icons';
import Logo from './assets/logo.webp';
import {
    useSideBar,
    useAuth,
    useCreateEntity,
    getUserIdFromUserInfo,
    useGlobalConfig,
    useWorkspace,
} from '@glidesystems/caching-store';
import SearchBar from './components/SearchBar/SearchBar';
import { ManageBookmarks } from './components/Bookmarks/ManageBookmarks';
import CreateReport from './components/CreateReport/CreateReport';
import WorkspaceSelector from './components/WorkspaceSelector/WorkspaceSelector';
import { Inbox } from '@novu/react';

const CreatePaletteButton = styled(Button)(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
        '& .text': {
            display: 'none',
        },
        '& .MuiButton-endIcon': {
            marginLeft: '-2px',
        },
        width: '32px',
        minWidth: '32px',
    },
}));

export default function Header() {
    const toggleCreateEntity = useCreateEntity((state) => state.toggle);
    const userInfo = useAuth((state) => state.userInfo);
    const navigate = useNavigate();
    const theme = useTheme();
    const isTablet = useMediaQuery(theme.breakpoints.down('sm'));
    const { removeWorkspace } = useWorkspace();
    const [isConfigLoaded, workspaceFeature] = useGlobalConfig((state) => [
        state.isLoaded,
        state.getWorkspaceFeature(),
    ]);
    const isWorkspaceFeatureEnabled = workspaceFeature === 'true';

    const [open, toggle] = useSideBar((state) => [state.open, state.toggle]);
    const [bookmarkAnchorEl, setBookmarkAnchorEl] = useState(null);

    useEffect(() => {
        // If workspace feature is disabled, remove workspace name from local storage
        if (isConfigLoaded && !isWorkspaceFeatureEnabled) {
            removeWorkspace();
        }
    }, [isConfigLoaded, workspaceFeature]);

    return (
        <>
            <Box
                sx={{
                    '& .MuiPaper-root': {
                        boxShadow: 'none',
                        minHeight: '52px',
                        height: '52px',
                        display: 'flex',
                        justifyContent: 'center',
                        backgroundColor: theme.palette.glide.background.normal.quarternary,
                    },
                }}
            >
                <AppBar>
                    <Toolbar disableGutters>
                        <IconButton
                            onClick={toggle}
                            sx={{ p: 0, mx: '12px', color: theme.palette.glide.text.normal.tertiary }}
                        >
                            <CollapseIcon sx={{ transform: open ? 'none' : 'rotate(180deg)' }} />
                        </IconButton>
                        {!isTablet && (
                            <Link to="/dashboard" style={{ marginRight: '24px' }} aria-disabled className='none-pe-when-disabled'>
                                <img src={Logo} alt="GlideSysLM Platform" style={{ width: '166px', height: '36px' }} />
                            </Link>
                        )}
                        <SearchBar />
                        <Box
                            sx={{
                                display: 'flex',
                                mr: '16px',
                                alignItems: 'center',
                            }}
                        >
                            <CreatePaletteButton
                                size="small"
                                variant="contained-white"
                                onClick={toggleCreateEntity}
                                endIcon={
                                    <PlusIcon
                                        sx={{
                                            width: 16,
                                            height: 16,
                                        }}
                                    />
                                }
                                sx={{
                                    marginY: 'auto',
                                }}
                                className='dim-when-disabled none-pe-when-disabled'
                            >
                                <span className="text">Create Palette</span>
                            </CreatePaletteButton>
                            <Box
                                sx={{
                                    minHeight: '18px',
                                    minWidth: '18px',
                                    marginLeft: '16px',
                                    '& .nv-notificationPrimaryAction__button, .nv-notificationSecondaryAction__button':
                                        {
                                            '&::before': {
                                                content: 'none',
                                            },
                                            '&::after': {
                                                content: 'none',
                                            },
                                        },
                                    '& .nv-inboxHeader': {
                                        '& .nv-icon, .nv-dots': {
                                            color: theme.palette.glide.text.normal.tertiary,
                                        },
                                    },
                                    '& .nv-notificationList': {
                                        '> div': {
                                            borderBottom: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                        },
                                    },
                                }}
                                className='dim-when-disabled none-pe-when-disabled'
                            >
                                {userInfo && (
                                    <Inbox
                                        appearance={{
                                            elements: {
                                                button: {
                                                    borderRadius: '2px',
                                                    backgroundImage: 'none',
                                                    border: 'none',
                                                    outline: 'none',
                                                    padding: '6px 16px',
                                                },
                                                inboxHeader: {
                                                    background: theme.palette.glide.background.normal.tertiary,
                                                    color: theme.palette.glide.text.normal.tertiary,
                                                },
                                            },
                                            baseTheme: {
                                                variables: {
                                                    borderRadius: '4px',
                                                    colorBackground: theme.palette.glide.background.normal.white,
                                                    colorPrimary: theme.palette.glide.background.normal.quarternary,
                                                    colorPrimaryForeground: theme.palette.glide.text.normal.tertiary,
                                                    colorSecondary:
                                                        theme.palette.glide.background.normal.inverseSecondary,
                                                    colorSecondaryForeground: theme.palette.glide.text.normal.main,
                                                },
                                            },
                                            icons: {
                                                bell: () => (
                                                    <IconButton size="small">
                                                        <NotificationIcon
                                                            style={{ color: 'white', width: '18px', height: '18px' }}
                                                        />
                                                    </IconButton>
                                                ),
                                            },
                                        }}
                                        applicationIdentifier={process.env.NOTIFICATION_APPLICATION_IDENTIFIER}
                                        subscriber={getUserIdFromUserInfo(userInfo)}
                                        backendUrl={process.env.NOTIFICATION_URL}
                                        socketUrl={process.env.NOTIFICATION_SOCKET_URL}
                                        routerPush={(path: string) => navigate(path)}
                                    />
                                )}
                            </Box>
                            <IconButton
                                size="small"
                                onClick={(e) => {
                                    if (bookmarkAnchorEl === null) {
                                        setBookmarkAnchorEl(e.currentTarget);
                                    } else {
                                        setBookmarkAnchorEl(null);
                                    }
                                }}
                                className='dim-when-disabled none-pe-when-disabled'
                            >
                                <BookmarkOutlineIcon style={{ width: '18px', height: '18px' }} fill="white" />
                            </IconButton>
                            <Popper open={Boolean(bookmarkAnchorEl)} anchorEl={bookmarkAnchorEl}>
                                <ManageBookmarks
                                    onClickAway={() => {
                                        setBookmarkAnchorEl(null);
                                    }}
                                />
                            </Popper>
                            {isWorkspaceFeatureEnabled && (
                                <>
                                    <Divider
                                        orientation="vertical"
                                        flexItem
                                        sx={{
                                            mx: 1.5,
                                            alignSelf: 'stretch',
                                        }}
                                    />
                                    <WorkspaceSelector />
                                </>
                            )}
                        </Box>
                    </Toolbar>
                </AppBar>
            </Box>

            <Suspense fallback={<LoadingOverlay />}>
                <CreatePalette />
                <CreateReport />
            </Suspense>
        </>
    );
}
