/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { ColDef, GridOptions, ICellRendererParams } from '@ag-grid-community/core';
import { Attribute, EntityDetail, entityUrls, fetch, SYSTEM_ENTITY_TYPE, SYSTEM_RELATION } from '@glidesystems/api';
import {
    AnimatedPage,
    DEFAULT_TABLE_PAGINATION_SIZE,
    EntityNameRenderer,
    FilterIcon,
    GroupObjectIcon,
    Loading,
    LoadingOverlay,
    PropertyValueRenderer,
    tableIcons,
    TablePagination,
    tableStyles,
} from '@glidesystems/styleguide';
import SavedFilterSidebar from '../../components/SavedFilter/SavedFilter';
import FixedContainer from '../../components/Layout/FixedContainer';
import { Box, Typography } from '@mui/material';
import { AgGridReact } from '@ag-grid-community/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSchemaDetail } from '@glidesystems/caching-store';
import { useParams } from 'react-router-dom';
import NoRowsOverlay from '../../components/NoRowsOverlay/NoRowsOverlay';
import { ToolbarItem, ToolbarWrapper } from '../../components/Relations/Toolbar';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import { DEFAULT_DATETIME_CELL_WIDTH } from '../../constants/common';

const RELATION_SEARCH_QUERY = 'relation.SIP_DEFINED_FOR:ip[id,name,type,revision]';

const DEFAULT_COLUMN_DEFS: ColDef[] = [
    {
        field: 'properties.name',
        headerName: 'Name',
        filter: 'agTextColumnFilter',
        cellRenderer: (props: ICellRendererParams<EntityDetail>) => (
            <EntityNameRenderer
                {...props}
                value={props.value}
                data={{
                    id: props.data?.id,
                    ...props.data?.properties,
                }}
            />
        ),
        flex: 1,
        minWidth: 200,
    },
    {
        headerName: 'Dispositioned Product',
        field: 'relationInfo.relation.SIP_DEFINED_FOR:ip[id,name,type].name',
        filter: 'agTextColumnFilter',
        minWidth: DEFAULT_DATETIME_CELL_WIDTH,
        cellRenderer: (props: ICellRendererParams<EntityDetail>) => (
            <EntityNameRenderer
                {...props}
                value={
                    props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.name
                        ? props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.name +
                          ' ' +
                          props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.revision
                        : props.node?.field === `relationInfo.${RELATION_SEARCH_QUERY}.name`
                        ? props.node?.key
                        : ''
                }
                data={{
                    id: props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.id,
                    type: props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.type,
                    name: props.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.name,
                }}
            />
        ),
        valueGetter: (params) =>
            params.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.name
                ? params.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.name +
                  ' ' +
                  params.data?.relationInfo?.[RELATION_SEARCH_QUERY]?.[0]?.revision
                : params.node?.field === `relationInfo.${RELATION_SEARCH_QUERY}.name`
                ? params.node?.key
                : '',
    },
];

const EXCLUDED_ATTRIBTUES = ['name'];

const getColDef = (attribute: Attribute): ColDef<EntityDetail> => ({
    field: `properties.${attribute.name}`,
    headerName: attribute.displayName,
    flex: 1,
    minWidth: 120,
    editable: false,
    filter: 'agTextColumnFilter',
    autoHeight: true,
    cellStyle: () => {
        if (attribute.name === 'revision') {
            return {
                color: '#52C41A',
            };
        }
    },
    cellRenderer: PropertyValueRenderer,
    cellRendererParams: {
        schema: attribute,
    },
    enableRowGroup: true,
});

const buildColDefs = (attributes: Record<string, Attribute>, attributeOrder: string[]) => {
    const sortedColDefs: ColDef[] = [];
    const sortedKeys = ['name'];
    sortedColDefs.push(...DEFAULT_COLUMN_DEFS);
    attributeOrder.forEach((order) => {
        if (!order.includes(':')) {
            if (EXCLUDED_ATTRIBTUES.includes(order)) return;
            const attribute = attributes[order];
            if (!attribute?.visible) return;
            sortedColDefs.push(getColDef(attribute));
            sortedKeys.push(order);
            return;
        }
        const groupAttributes = order.split(':')[1].split(',') ?? [];

        groupAttributes.forEach((groupAttr) => {
            if (EXCLUDED_ATTRIBTUES.includes(groupAttr)) return;
            const attribute = attributes[groupAttr];
            if (!attribute?.visible) return;
            sortedColDefs.push(getColDef(attribute));
            sortedKeys.push(groupAttr);
        });
    });
    Object.values(attributes ?? {}).forEach((attribute) => {
        if (!sortedKeys.includes(attribute.name) && attribute?.visible) {
            sortedColDefs.push(getColDef(attribute));
        }
    });
    return sortedColDefs;
};

const GRID_OPTIONS: GridOptions<EntityDetail> = {
    loadingOverlayComponent: Loading,
    rowDragMultiRow: true,
    animateRows: true,
    defaultColDef: {
        sortable: true,
        resizable: true,
        flex: 1,
        filter: true,
        floatingFilter: false,
        enablePivot: true,
        autoHeight: true,
        wrapText: true,
        minWidth: 150,
    },
    suppressMenuHide: true,
    rowModelType: 'clientSide',
    rowSelection: 'multiple',
    headerHeight: 34,
    rowHeight: 40,
    groupHeaderHeight: 34,
    floatingFiltersHeight: 34,
    icons: tableIcons,
    sideBar: {
        toolPanels: [
            {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
            },
            {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
            },
            {
                id: 'saved-filters',
                labelDefault: 'Saved Filters',
                labelKey: 'saved-filters',
                iconKey: 'filter',
                toolPanel: SavedFilterSidebar,
                toolPanelParams: {
                    type: 'Dispositions',
                    dropZoneId: 'bug-dispositions-table',
                },
            },
        ],
    },
    rowStyle: {
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
    },
    enableCharts: true,
    enableRangeSelection: true,
    cacheBlockSize: 100,
    pagination: true,
    suppressPaginationPanel: true,
};

export const DispositionsOnBug = () => {
    const gridRef = useRef<AgGridReact>(null);
    const { entityType, id: entityId } = useParams();
    const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);
    const { getSchema, schema } = useSchemaDetail();
    const [isPaginationOn, setPaginationOn] = useState<boolean>(true);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(DEFAULT_TABLE_PAGINATION_SIZE);
    const enableRowGroupPanel = useRef(false);
    const [errorFetchingData, setErrorFetchingData] = useState(false);
    const {
        detailEntity,
        sessions: { isLoadingEntityDetail },
    } = useSelector(selectAppReducer);

    const handleCurrentPageChanged = useCallback(
        (value) => {
            gridRef.current?.api.paginationGoToPage(value - 1);
            setCurrentPage(value);
        },
        [setCurrentPage]
    );

    const onPageSizeChanged = useCallback((value) => {
        gridRef.current?.api.paginationSetPageSize(value);
    }, []);

    const handleOnOffPagination = useCallback(
        (value) => {
            setPaginationOn(value);
            if (value) {
                gridRef.current?.api.paginationSetPageSize(DEFAULT_TABLE_PAGINATION_SIZE);
            } else {
                gridRef.current?.api.paginationSetPageSize(gridRef.current?.api.getDisplayedRowCount() + 1);
            }
        },
        [setPaginationOn]
    );

    const onPaginationChanged = useCallback(() => {
        if (gridRef.current?.api) {
            setCurrentPage(gridRef.current.api.paginationGetCurrentPage() + 1);
            setTotalPages(gridRef.current.api.paginationGetTotalPages());
            setPageSize(gridRef.current.api.paginationGetPageSize());
        }
    }, []);

    const colDefs = useMemo(() => {
        if (!schema?.[SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]?.attributes) {
            return [];
        }
        return buildColDefs(
            schema[SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]?.attributes,
            schema[SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]?.entityType.attributeOrder
        );
    }, [schema]);

    const toggleRowGroupPanel = useCallback(() => {
        enableRowGroupPanel.current = !enableRowGroupPanel.current;
        gridRef.current?.api.setRowGroupPanelShow(enableRowGroupPanel.current ? 'always' : 'never');
    }, []);

    const toggleColumnFilters = useCallback(() => {
        let colDefs = gridRef.current?.api.getColumnDefs() || [];
        const enabled = !Boolean(colDefs.some((colDef: any) => colDef.floatingFilter));
        colDefs.forEach((colDef: any) => {
            colDef.floatingFilter = enabled;
            if (colDef.children) {
                colDef.children.forEach((child) => (child.floatingFilter = enabled));
            }
        });
        gridRef.current?.api.setColumnDefs(colDefs);
        gridRef.current?.api.refreshHeader();
    }, []);

    useEffect(() => {
        getSchema(SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION);
    }, []);
    return isLoadingEntityDetail ? (
        <LoadingOverlay />
    ) : (
        <AnimatedPage>
            <FixedContainer sx={{ backgroundColor: '#FFFFFF' }} fixedOnTablet>
                <Box
                    sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            height: '100%',
                            ...tableStyles,
                        }}
                    >
                        <ToolbarWrapper>
                            <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                <ToolbarItem
                                    icon={<FilterIcon />}
                                    defaultMsg="Column Filters"
                                    onClick={toggleColumnFilters}
                                />
                                <ToolbarItem
                                    icon={<GroupObjectIcon />}
                                    defaultMsg="Toggle row group panel"
                                    onClick={toggleRowGroupPanel}
                                />
                            </Box>
                        </ToolbarWrapper>
                        <Box
                            id="impact-analysis-table"
                            sx={{
                                height: '100%',
                                width: '100%',
                                borderRadius: 8,
                                marginBottom: '8px',
                                marginLeft: '0px',
                            }}
                            className="ag-theme-alpine"
                        >
                            <AgGridReact
                                {...GRID_OPTIONS}
                                ref={gridRef}
                                columnDefs={colDefs}
                                onGridReady={({ api }) => {
                                    fetch({
                                        ...entityUrls.getEntityRelations,
                                        params: {
                                            entityType: SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION,
                                            fromEntityId: entityId,
                                            relationType: SYSTEM_RELATION.SIP_DISPOSITIONED_BUG,
                                        },
                                        qs: {
                                            limit: 5000,
                                            offset: 0,
                                            fields: RELATION_SEARCH_QUERY,
                                            reverse: true,
                                        },
                                    })
                                        .then((response) => {
                                            api.setRowData(response.data.data);
                                        })
                                        .catch((e) => {
                                            console.error('Error fetching data. ' + e);
                                            setErrorFetchingData(true);
                                            api.setRowData([]);
                                        })
                                        .finally(() => {
                                            if (api.getDisplayedRowCount() === 0) {
                                                api.showNoRowsOverlay();
                                            }
                                        });
                                }}
                                onPaginationChanged={onPaginationChanged}
                                noRowsOverlayComponent={NoRowsOverlay}
                                noRowsOverlayComponentParams={{
                                    error: errorFetchingData,
                                    message: errorFetchingData ? (
                                        `There was an error fetching the Dispositions for ${detailEntity?.properties.name}`
                                    ) : (
                                        <Typography sx={{ mt: 2 }}>
                                            There are no Dispositions created for {detailEntity?.properties.name}
                                        </Typography>
                                    ),
                                }}
                            />
                        </Box>
                    </Box>
                    <TablePagination
                        totalPages={totalPages}
                        currentPage={currentPage}
                        pageSize={pageSize}
                        onCurrentPageChanged={handleCurrentPageChanged}
                        onPageSizeChanged={onPageSizeChanged}
                        onPaginationModeChange={handleOnOffPagination}
                        isPaginationOn={isPaginationOn}
                    />
                </Box>
            </FixedContainer>
        </AnimatedPage>
    );
};
