/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useSelector } from 'react-redux';
import { FullHeightGrid, ContentWrapper, AnimatedContainer } from '../../components/Layout/Common';
import { selectAppReducer } from '../../selectors';
import AttributeDetail from '../../components/AttributeDetail/AttributeDetail';
import ClassificationAttributes from '../../components/AttributeDetail/ClassificationAttributes';
import AffectedItemPlans from '../../components/AttributeDetail/AffectedItemPlans';
import { SYSTEM_RELATION, entityUrls, DEFAULT_CLIENT_SIDE_LIMIT, fetch } from '@glidesystems/api';
import { EntityNameRenderer, formatDate, LinkIcon, PropertyValueRenderer, UploadIcon } from '@glidesystems/styleguide';
import EntityRelation from '../../components/AttributeDetail/EntityRelation';
import moment from 'moment';
import get from 'lodash/get';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Theme, useMediaQuery } from '@mui/material';
import RelationAttribute from '../../components/AttributeDetail/RelationAttribute';

const LeftPanel = () => {
    const { detailEntity, detailSchema, classificationSchema } = useSelector(selectAppReducer);
    return (
        <FullHeightGrid xs={12} md={'auto'} item>
            <ContentWrapper>
                <AnimatedContainer
                    animation={{
                        initial: { opacity: 0, scale: 0.98 },
                        animate: { opacity: 1, scale: 1 },
                    }}
                    className="leftPanel"
                >
                    <AttributeDetail
                        title="General Information"
                        data={detailEntity}
                        detailSchema={detailSchema}
                        showMaster={false}
                        propertyGrid={{
                            xs: 6,
                            sm: 6,
                            md: 6,
                            lg: 6,
                        }}
                        subContent={
                            <>
                                <RelationAttribute
                                    detailEntity={detailEntity}
                                    relationNames={[SYSTEM_RELATION.REQUESTED_ON, SYSTEM_RELATION.COMPLETED_ON]}
                                />
                            </>
                        }
                    />
                    <ClassificationAttributes
                        detailEntity={detailEntity}
                        classificationSchema={classificationSchema}
                        detailSchema={detailSchema}
                    />
                </AnimatedContainer>
            </ContentWrapper>
        </FullHeightGrid>
    );
};

const RightPanel = () => {
    const { detailEntity } = useSelector(selectAppReducer);

    return (
        <FullHeightGrid xs={12} md={'auto'} item className="rightPanel">
            <ContentWrapper>
                <AnimatedContainer>
                    <EntityRelation
                        detailEntity={detailEntity}
                        relationType={SYSTEM_RELATION.REFERENCE_TO}
                        canCreateNew
                        createNewLabel="Upload"
                        createNewIcon={<UploadIcon />}
                    />
                </AnimatedContainer>
            </ContentWrapper>
        </FullHeightGrid>
    );
};

const IPChangeRequestAttributesView = () => {
    const {
        sessions: { isLoadingEntityDetail },
    } = useSelector(selectAppReducer);
    const isLargeScreen = useMediaQuery((theme: Theme) => theme.breakpoints.up('md'));
    if (isLoadingEntityDetail) {
        return <></>;
    }
    if (!isLargeScreen) {
        return (
            <>
                <LeftPanel />
                <RightPanel />
            </>
        );
    }

    return (
        <PanelGroup
            className="panelGroup"
            style={{ display: 'flex', flexDirection: isLargeScreen ? 'row' : 'column' }}
            direction={isLargeScreen ? 'horizontal' : 'vertical'}
            autoSaveId="change-request-attribute-view"
        >
            <Panel
                id="change-request-attribute-view-panel"
                style={{ height: '100%' }}
                minSize={20}
                defaultSize={40}
                maxSize={80}
            >
                <LeftPanel />
            </Panel>
            <Panel minSize={20} defaultSize={60} maxSize={80} style={{ height: '100%', position: 'relative' }}>
                <PanelResizeHandle
                    style={{
                        position: 'absolute',
                        height: '100%',
                        width: '8px',
                        left: 0,
                        top: 0,
                        zIndex: 10,
                    }}
                ></PanelResizeHandle>
                <RightPanel />
            </Panel>
        </PanelGroup>
    );
};

export default IPChangeRequestAttributesView;
