/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { AnimatedContainer, ContentWrapper, FullHeightGrid } from '../../components/Layout/Common';
import AttributeDetail from '../../components/AttributeDetail/AttributeDetail';
import ClassificationAttributes from '../../components/AttributeDetail/ClassificationAttributes';
import { useDispatch, useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import {
    Autocomplete,
    Avatar,
    Box,
    Button,
    FormHelperText,
    Grid,
    IconButton,
    TextField,
    Typography,
} from '@mui/material';
import {
    AgentDropdown,
    CloseIcon,
    DEFAULT_CELL_WIDTH,
    DeleteIcon,
    DRAWER_COMPONENT_NAME,
    LoadingOverlay,
    MainTooltip,
    MinusIcon,
    NoRowsOverlay,
    notifyError,
    notifySuccess,
    PlusIcon,
    ResizableDrawer,
    tableIcons,
    tableStyles,
} from '@glidesystems/styleguide';
import { AgGridReact } from '@ag-grid-community/react';
import type { GridOptions } from '@ag-grid-community/core';
import {
    fetch as apiFetch,
    batchRequestBody,
    DEFAULT_CLIENT_SIDE_LIMIT,
    EntityDetail,
    entityUrls,
    getAvatarUrl,
    Method,
    SYSTEM_RELATION,
} from '@glidesystems/api';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { EntityRelation } from '../../models/models';
import { useRoles } from '@glidesystems/caching-store';
import { ICellRendererParams } from 'ag-grid-community';
import { onOpenModal } from '../../actions';
import * as Yup from 'yup';
import { useFormik } from 'formik';

function stringToColor(string: string) {
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;

    const saturation = 65;
    const lightness = 72;

    return `hsl(${hue}deg ${saturation}% ${lightness}%)`;
}
const AssigneeAvatar = ({ name, email }: { name: string; email?: string }) => {
    return (
        <MainTooltip title={email}>
            <Avatar
                sizes="small"
                sx={{ bgcolor: stringToColor(name), width: 26, height: 26, border: '1px solid white' }}
                children={`${name.split(' ')[0][0]}${name.split(' ')?.[1]?.[0] || ''}`}
                className="avatar"
                alt={name}
                src={email ? getAvatarUrl(email) : ''}
            />
        </MainTooltip>
    );
};

const AssigneeRenderer = ({ value, data }) => {
    return (
        <Typography
            component="div"
            sx={{
                fontSize: '14px',
                fontWeight: 400,
                lineHeight: '16px',
                display: 'flex',
                gap: '8px',
                alignItems: 'center',
                padding: '4px 8px',
            }}
        >
            <AssigneeAvatar name={value} email={data.relation.email} />
            {value}
        </Typography>
    );
};

const RoleRenderer = (props: ICellRendererParams) => {
    const [value, setValue] = useState(props.value || '');
    const { id: entityId } = useParams();
    const { detailEntity } = useSelector(selectAppReducer);
    const permissionRoles = useRoles((state) => state.permissionRoles?.map((role) => role.name));
    const canUpdate =
        props.data.relation?.permissions?.canConnectAsToSide && detailEntity?.permissions?.canConnectAsFromSide;
    const handleChangeRole = useCallback(
        (_, newValue) => {
            setValue(newValue || '');
            apiFetch({
                ...entityUrls.updateRelation,
                params: {
                    fromEntityId: entityId,
                    relationId: props.data.id,
                },
                data: {
                    properties: {
                        permissionRole: newValue,
                    },
                },
            })
                .then(() => {
                    props.api.applyTransaction({
                        update: [{ ...props.data, properties: { ...props.data.properties, permissionRole: newValue } }],
                    });
                })
                .catch((error) => {
                    console.error(error);
                    setValue(props.value || '');
                });
        },
        [entityId]
    );
    return (
        <Autocomplete
            disabled={!permissionRoles || !canUpdate}
            options={permissionRoles}
            value={value}
            size="medium"
            onChange={handleChangeRole}
            fullWidth
            disableClearable
            renderInput={(params) => <TextField {...params} variant="standard" placeholder="Select Role" />}
        />
    );
};

const gridOptions: GridOptions = {
    headerHeight: 34,
    rowHeight: 40,
    floatingFiltersHeight: 36,
    columnDefs: [
        {
            field: 'relation.name',
            headerName: 'Assignee',
            cellRenderer: AssigneeRenderer,
            flex: 2,
            filter: 'agTextColumnFilter',
            minWidth: DEFAULT_CELL_WIDTH,
            headerTooltip: 'Assignee Name',
            floatingFilter: true,
            checkboxSelection: true,
        },
        {
            field: 'properties.permissionRole',
            headerName: 'Role',
            flex: 1,
            filter: 'agTextColumnFilter',
            floatingFilter: true,
            minWidth: DEFAULT_CELL_WIDTH,
            cellRenderer: RoleRenderer,
        },
    ],
    loadingOverlayComponent: LoadingOverlay,
    defaultColDef: {
        minWidth: 100,
        sortable: true,
        resizable: true,
        filter: true,
    },
    suppressColumnVirtualisation: true,
    suppressMenuHide: true,
    icons: tableIcons,
    rowSelection: 'multiple',
    getRowId: (params) => params.data.id,
    suppressRowClickSelection: true,
    rowStyle: {
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
    },
    rowModelType: 'clientSide',
    noRowsOverlayComponent: NoRowsOverlay,
    noRowsOverlayComponentParams: {
        defaultMessage: 'No assignees found',
    },
};

const LeftPanel = () => {
    const { detailEntity, detailSchema, classificationSchema } = useSelector(selectAppReducer);
    return (
        <FullHeightGrid xs={12} md={'auto'} item className="leftPanel">
            <ContentWrapper>
                <AnimatedContainer
                    animation={{
                        initial: { opacity: 0, scale: 0.98 },
                        animate: { opacity: 1, scale: 1 },
                    }}
                >
                    <AttributeDetail
                        title="General Information"
                        data={detailEntity}
                        detailSchema={detailSchema}
                        showMaster={false}
                    />
                    <ClassificationAttributes
                        detailEntity={detailEntity}
                        classificationSchema={classificationSchema}
                        detailSchema={detailSchema}
                    />
                </AnimatedContainer>
            </ContentWrapper>
        </FullHeightGrid>
    );
};

function AddAssignee({ entityId, refreshData }: { entityId: string; refreshData: () => void }) {
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);
    const permissionRoles = useRoles((state) => state.permissionRoles?.map((role) => role.name));
    const [isLoading, setIsLoading] = useState(false);
    const validationSchema = Yup.object({
        rows: Yup.array()
            .of(
                Yup.object({
                    assignee: Yup.object().nullable().required('Assignee is required'),
                    permissionRole: Yup.string().nullable().required('Permission role is required'),
                })
            )
            .min(1, 'At least one assignee is required'),
    });

    const formik = useFormik<{
        rows: { assignee: EntityDetail | null; permissionRole: string | null }[];
    }>({
        initialValues: {
            rows: [{ assignee: null, permissionRole: null }],
        },
        validationSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: async (values) => {
            try {
                setIsLoading(true);
                const { rows } = values;
                const params = rows.map(({ assignee, permissionRole }) => ({
                    subParams: {
                        fromEntityId: entityId,
                        relationType: SYSTEM_RELATION.ACCESSOR,
                        toEntityId: assignee.id,
                    },
                    body: {
                        properties: {
                            permissionRole,
                        },
                    },
                }));
                const batchRequest = batchRequestBody(
                    Method.POST,
                    `/entity/:fromEntityId/:relationType/:toEntityId`,
                    params
                );
                const { data } = await apiFetch({
                    ...entityUrls.batchRequest,
                    data: batchRequest,
                });
                if (data.some((res) => !res.success || res.status != 200)) {
                    notifyError(`An error occurred while adding agents to the created project`);
                    return;
                }
                refreshData();
                notifySuccess(`Agents have been added to the project successfully`);
                handleClose();
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        },
    });

    const rows = formik.values.rows;

    return (
        <>
            {isLoading && <LoadingOverlay />}
            <IconButton color="primary" size="small" onClick={handleOpen}>
                <PlusIcon />
            </IconButton>
            <ResizableDrawer
                open={open}
                onClose={handleClose}
                componentName={DRAWER_COMPONENT_NAME.CREATE_PROJECT}
                defaultWidth={650}
                minWidth={475}
                disableCloseOnClickOutside
                disableEnforceFocus
            >
                <Box
                    sx={{
                        display: 'flex',
                        color: '#545454',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: '24px',
                        backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            gap: '16px',
                            position: 'relative',
                        }}
                    >
                        <Typography
                            sx={{
                                fontSize: '20px',
                                lineHeight: '150%',
                                fontWeight: 600,
                                cursor: 'default',
                                color: (theme) => theme.palette.glide.text.white,
                            }}
                        >
                            Add Assignee
                        </Typography>
                        <IconButton
                            onClick={handleClose}
                            sx={{
                                height: '24px',
                                width: '24px',
                                p: 0,
                                color: (theme) => theme.palette.glide.text.white,
                                marginLeft: 'auto',
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </Box>
                <Box sx={{ flexGrow: 1, p: '24px', overflowY: 'auto' }}>
                    <Grid container spacing={2}>
                        {rows.map((row, index) => {
                            const assigneeError =
                                formik.touched.rows?.[index]?.assignee &&
                                (formik.errors.rows as any)?.[index]?.assignee;
                            const roleError =
                                formik.touched.rows?.[index]?.permissionRole &&
                                (formik.errors.rows as any)?.[index]?.permissionRole;

                            return (
                                <React.Fragment key={`row-${index}`}>
                                    <Grid item xs={8}>
                                        <AgentDropdown
                                            key={row.assignee?.id}
                                            value={row.assignee}
                                            onChange={(newValue: EntityDetail) => {
                                                formik.setFieldValue(`rows[${index}].assignee`, newValue, true);
                                                formik.setFieldTouched(`rows[${index}].assignee`, true, false);
                                            }}
                                        />
                                        {assigneeError && (
                                            <FormHelperText error>{String(assigneeError)}</FormHelperText>
                                        )}
                                    </Grid>
                                    <Grid item xs={4}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                            <Autocomplete
                                                options={permissionRoles}
                                                value={row.permissionRole}
                                                size="medium"
                                                onChange={(event, newValue) => {
                                                    formik.setFieldValue(
                                                        `rows[${index}].permissionRole`,
                                                        newValue,
                                                        true
                                                    );
                                                    formik.setFieldTouched(
                                                        `rows[${index}].permissionRole`,
                                                        true,
                                                        false
                                                    );
                                                }}
                                                fullWidth
                                                disableClearable
                                                onBlur={() => formik.setFieldTouched(`rows[${index}].permissionRole`)}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        variant="standard"
                                                        placeholder="Select Role"
                                                        required
                                                        error={Boolean(roleError)}
                                                        helperText={roleError ? String(roleError) : ''}
                                                    />
                                                )}
                                            />
                                            <IconButton
                                                size="small"
                                                onClick={() =>
                                                    formik.setFieldValue(
                                                        'rows',
                                                        [
                                                            ...rows.slice(0, index + 1),
                                                            { assignee: null, permissionRole: null },
                                                            ...rows.slice(index + 1),
                                                        ],
                                                        true
                                                    )
                                                }
                                            >
                                                <PlusIcon />
                                            </IconButton>
                                            {rows.length > 1 && (
                                                <IconButton
                                                    size="small"
                                                    onClick={() =>
                                                        formik.setFieldValue(
                                                            'rows',
                                                            rows.filter((_, i) => i !== index),
                                                            true
                                                        )
                                                    }
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            )}
                                        </Box>
                                    </Grid>
                                </React.Fragment>
                            );
                        })}
                    </Grid>
                </Box>
                <Box
                    sx={{
                        marginTop: 'auto',
                        p: '24px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'flex-end',
                        gap: '8px',
                    }}
                >
                    <Button
                        sx={{
                            width: { xs: '120px', msFlexDirection: '160px' },
                            justifyContent: 'flex-start',
                        }}
                        variant="contained"
                        color="secondary"
                        size="medium"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>
                    <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-start' }}>
                        <Button
                            onClick={() => formik.handleSubmit()}
                            className="actionBtn"
                            variant="contained"
                            color="primary"
                            size="medium"
                            sx={{ minWidth: '180px', justifyContent: 'flex-start' }}
                        >
                            Add
                        </Button>
                    </Box>
                </Box>
            </ResizableDrawer>
        </>
    );
}

/**
 *
 * @returns Assignees table
 */
const RightPanel = () => {
    const { id: entityId } = useParams();
    const { detailEntity } = useSelector(selectAppReducer);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedRows, setSelectedRows] = useState<EntityRelation[]>([]);
    const getPermissionRoles = useRoles((state) => state.getPermissionRoles);
    const dispatch = useDispatch();
    const gridRef = useRef<AgGridReact>(null);
    const [data, setData] = useState<EntityRelation[]>(null);
    const fetchData = useCallback(async () => {
        const res = await apiFetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: {
                entityId,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                relationNames: SYSTEM_RELATION.ACCESSOR,
            },
        });
        setData(res?.data?.data || []);
    }, [entityId]);

    const handleSelectionChanged = () => {
        setSelectedRows(gridRef.current.api.getSelectedRows());
    };

    const removeAssigness = async () => {
        try {
            setIsLoading(true);
            const requests = selectedRows.map((row) =>
                apiFetch({
                    ...entityUrls.deleteRelationByRelationId,
                    params: {
                        fromEntityId: entityId,
                        relationId: row.id,
                    },
                })
            );
            await Promise.all(requests);
            await fetchData();
            setSelectedRows([]);
            notifySuccess('Successfully removed selected assignees');
        } catch (err) {
            console.error(err);
            notifyError('Failed to remove selected assignees');
        } finally {
            setIsLoading(false);
        }
    };

    const onRemove = () => {
        dispatch(
            onOpenModal({
                open: true,
                title: 'Remove selected assignees',
                content: 'Do you really want to remove selected assignees from the current Project?',
                onModalNextAction: removeAssigness,
                action: 'confirm',
                confirmColor: 'error',
            })
        );
    };

    useEffect(() => {
        fetchData();
        getPermissionRoles();
    }, [fetchData]);

    return (
        <FullHeightGrid item xs={12} md={'auto'}>
            {isLoading && <LoadingOverlay />}
            <ContentWrapper>
                <AnimatedContainer style={{ height: '100%' }}>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            padding: '16px',
                            height: '100%',
                            ...tableStyles,
                            gap: '4px',
                        }}
                    >
                        <Typography variant="ol1">Assignee</Typography>
                        <Box sx={{ gap: '4px', display: 'flex', alignItems: 'center' }}>
                            <AddAssignee refreshData={fetchData} entityId={entityId} />
                            <IconButton
                                disabled={
                                    selectedRows.length === 0 ||
                                    selectedRows.some((row) => !row.relation?.permissions?.canDisconnectAsToSide)
                                }
                                onClick={onRemove}
                                size="small"
                                color="primary"
                            >
                                <MinusIcon />
                            </IconButton>
                        </Box>
                        <Box sx={{ height: '100%' }}>
                            <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
                                <AgGridReact
                                    onSelectionChanged={handleSelectionChanged}
                                    rowData={data}
                                    {...gridOptions}
                                    ref={gridRef}
                                />
                            </div>
                        </Box>
                    </Box>
                </AnimatedContainer>
            </ContentWrapper>
        </FullHeightGrid>
    );
};

const ProjectAttributesView = () => {
    return (
        <PanelGroup direction="horizontal" autoSaveId="project-attribute-view">
            <Panel
                id="project-attribute-view-panel"
                style={{ height: '100%' }}
                minSize={20}
                defaultSize={40}
                maxSize={80}
            >
                {/* {isUpdating && <LoadingOverlay />} */}
                <LeftPanel />
            </Panel>
            <Panel minSize={20} defaultSize={60} maxSize={80} style={{ height: '100%', position: 'relative' }}>
                <PanelResizeHandle
                    style={{
                        position: 'absolute',
                        height: '100%',
                        width: '8px',
                        left: 0,
                        top: 0,
                        zIndex: 10,
                    }}
                ></PanelResizeHandle>
                <RightPanel />
            </Panel>
        </PanelGroup>
    );
};

export default ProjectAttributesView;
