/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
const actionTypes = {
    LOADING: 'LOADING',
    LOADING_DETAIL_ENTITY: 'LOADING_DETAIL_ENTITY',
    SET_LOADED: 'SET_LOADED',
    LOADING_SCHEMA_DETAIL: 'LOADING_SCHEMA_DETAIL',
    SET_DETAIL_ENTITY: 'SET_DETAIL_ENTITY',
    UPDATE_DETAIL_ENTITY: 'UPDATE_DETAIL_ENTITY',
    SET_SCHEMA_DETAIL: 'SET_SCHEMA_DETAIL',
    SET_DIGITAL_THREADS: 'SET_DIGITAL_THREADS',
    SET_REVISION_SCHEMA_DETAIL: 'SET_REVISION_SCHEMA_DETAIL',
    SET_DECOMPOSITION_SCHEMA: 'SET_DECOMPOSITION_SCHEMA',
    LOADING_APPLYING_CLASSIFICATIONS: 'LOADING_APPLYING_CLASSIFICATIONS',
    LOADING_UPDATING_PROPERTIES: 'LOADING_UPDATING_PROPERTIES',
    SET_FLATTEN_TREE: 'SET_FLATTEN_TREE',
    UPDATE_WIDGET_CONTAINER: 'UPDATE_WIDGET_CONTAINER',
    ON_OPEN_MODAL: 'ON_OPEN_MODAL',
    ON_CLOSE_MODAL: 'ON_CLOSE_MODAL',
    ON_RESET_MODAL: 'ON_RESET_MODAL',
    LOADING_ADD_BOM: 'LOADING_ADD_BOM',
    LOADING_GET_RELATIONS_SUMMARY: 'LOADING_GET_RELATION_SUMMARY',
    SET_SELECTED_BOMS: 'SET_SELECTED_BOMS',
    DISCONNECT_BOMs: 'DISCONNECT_BOMS',
    LOADING_REMOVE_BOM: 'LOADING_REMOVE_BOM',
    LOADING_GRANT_ACCESS: 'LOADING_GRANT_ACCESS',
    LOADING_DELETE_GLOBAL_ALTERNATE: 'LOADING_DELETE_GLOBAL_ALTERNATE',
    LOADING_LIFECYCLE_DETAIL: 'LOADING_LIFECYCLE_DETAIL',
    LOADING_ENTITY_STATE_HISTORY: 'LOADING_ENTITY_STATE_HISTORY',
    SET_LIFECYCLE_DETAIL: 'SET_LIFECYCLE_DETAIL',
    SET_ENTITY_STATE_HISTORY: 'SET_ENTITY_STATE_HISTORY',
    LOADING_ACCESSES: 'LOADING_ACCESSES',
    SET_ACCESSES: 'SET_ACCESSES',
    STORE_AGENT: 'STORE_AGENT',
    STORE_ENTITY_LIFECYCLES: 'STORE_ENTITY_LIFECYCLES',
    ON_GETTING_ENTITY_LIFECYCLES: 'ON_GETTING_ENTITY_LIFECYCLES',
    SET_DOCUMENT: 'SET_DOCUMENT',
    SET_MASTER_DETAILS: 'SET_MASTER_DETAILS',
    SET_MASTER_SCHEMA: 'SET_MASTER_SCHEMA',
    SET_MASTER_REVISON_DATA: 'SET_MASTER_REVISON_DATA',
    UPDATE_MASTER_DETAIL_ENTITY: 'UPDATE_MASTER_DETAIL_ENTITY',
    LOADING_RELATION_ENTITIES: 'LOADING_RELATION_ENTITIES',
    SET_ENTITY_MODEL: 'SET_ENTITY_MODEL',
    SET_IS_MATER_REVISION_MODEL: 'SET_IS_MATER_REVISION_MODEL',
    LOADING_BOM_TREE: 'LOADING_BOM_TREE',
    LOADING_WHERE_USED: 'LOADING_WHERE_USED',
    SET_WHERE_USED: 'SET_WHERE_USED',
    SET_BOM_TREE: 'SET_BOM_TREE',
    SET_RECURSIVE_COMPONENTS: 'SET_RECURSIVE_COMPONENTS',
    SET_RECURSIVE_ASSEMBLIES: 'SET_RECURSIVE_ASSEMBLIES',
    SET_CLASSIFICATION_SCHEMA: 'SET_CLASSIFICATION_SCHEMA',
    SET_CHANGED_ENTITY: 'SET_CHANGED_ENTITY',
    SET_CHANGED_ENTITY_MASTER: 'SET_CHANGED_ENTITY_MASTER',
    SET_CHANGED_ENTITY_SCHEMA: 'SET_CHANGED_ENTITY_SCHEMA',
    SET_CHANGED_ENTITY_MASTER_SCHEMA: 'SET_CHANGED_ENTITY_MASTER_SCHEMA',
    SET_LOADING_CHANGED_ENTITY: 'SET_LOADING_CHANGED_ENTITY',
    SET_LOADING_BOM_PLANNED_CHANGE_SUMMARY: 'SET_LOADING_BOM_PLANNED_CHANGE_SUMMARY',
    SET_BOM_PLANNED_CHANGE_SUMMARY: 'SET_BOM_PLANNED_CHANGE_SUMMARY',
    LOADING_ISSUE_SUMMARY: 'LOADING_ISSUE_SUMMARY',
    SET_ISSUE_SUMMARY: 'SET_ISSUE_SUMMARY',
    LOADING_REVISION_SUMMARY: 'LOADING_REVISION_SUMMARY',
    SET_REVISION_SUMMARY: 'SET_REVISION_SUMMARY',
    LOADING_CHANGED_ENTITY_BOM: 'LOADING_CHANGED_ENTITY_BOM',
    SET_CHANGED_ENTITY_BOM: 'SET_CHANGED_ENTITY_BOM',
    LOADING_CHANGED_ITEMS: 'LOADING_CHANGED_ITEMS',
    SET_CHANGED_ATTRIBUTE_ITEM: 'SET_CHANGED_ATTRIBUTE_ITEM',
    LOADING_UPDATE_PLANNED_CHANGE: 'LOADING_UPDATE_PLANNED_CHANGE',
    LOADING_AFFECTED_ITEMS: 'LOADING_AFFECTED_ITEMS',
    SET_AFFECTED_ITEMS: 'SET_AFFECTED_ITEMS',
    SET_SHOW_CHANGE_ITEMS_ONLY: 'SET_SHOW_CHANGE_ITEMS_ONLY',
    SET_SHOW_PLANNED_CHANGE: 'SET_SHOW_PLANNED_CHANGE',
    LOADING_CHANGE_STATE: 'LOADING_CHANGE_STATE',
    LOADING_BUSINESS_RULES: 'LOADING_BUSINESS_RULES',
    SET_BUSINESS_RULES: 'SET_BUSINESS_RULES',
    LOADING_INVOKE_RULES: 'LOADING_INVOKE_RULES',
    SET_INVOKED_RULES: 'SET_INVOKED_RULES',
    SET_SCHEMA: 'SET_SCHEMA',
    SET_ISSUE_SCHEMA: 'SET_ISSUE_SCHEMA',
    LOADING_OVERLAY: 'LOADING_OVERLAY',
    LOADING_RELATIONS: 'LOADING_RELATIONS',
    SET_RELATIONS: 'SET_RELATIONS',
    LOADING_COMPONENTS: 'LOADING_COMPONENTS',
    SET_COMPONENTS: 'SET_COMPONENTS',
    SET_PROPOSAL_OF_RELATIONS: 'SET_PROPOSAL_OF_RELATIONS',
    LOADING_PLANNED_CHANGE_IMPACT_ANALYSIS: 'LOADING_PLANNED_CHANGE_IMPACT_ANALYSIS',
    SET_IMPACT_ANALYSIS: 'SET_IMPACT_ANALYSIS',
    SET_MANUFACTURER_PARTS: 'SET_MANUFACTURER_PARTS',
    CLEAR_STORE: 'CLEAR_STORE',
    SET_HAS_APPLIED_PROCESS: 'SET_HAS_APPLIED_PROCESS',
    SET_REPORT_SCHEDULES: 'SET_REPORT_SCHEDULES',
    SET_REPORT_LAYOUT: 'SET_REPORT_LAYOUT',
};

export default actionTypes;
