/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled, Accordion as MuiAccordion } from '@mui/material';

const Accordion = styled(MuiAccordion)(({ theme }) => ({
    boxShadow: 'none',
    border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    borderRadius: '0 !important',
    '& .allowPointer': {
        pointerEvents: 'auto',
    },
    '& .summary': {
        height: '48px',
        minHeight: '48px',
        flexDirection: 'row-reverse',
        paddingLeft: '4px',
        backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        '& .MuiAccordionSummary-content': {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        '&.Mui-expanded': {
            height: '48px',
            minHeight: '48px',
        },
    },
    '& .accordionDetails': {
        padding: '0 16px 12px 16px',
    },
}));

export default Accordion;
