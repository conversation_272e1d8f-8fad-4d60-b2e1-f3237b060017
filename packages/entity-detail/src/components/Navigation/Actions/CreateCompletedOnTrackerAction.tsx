/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useState } from 'react';
import { useDraftForm } from '@glidesystems/caching-store';
import { useDispatch, useSelector } from 'react-redux';
import { batchRequest, getIssueSummary, onOpenModal } from '../../../actions';
import { PlusIcon, LinkIcon, dismiss, checkingPermission, notifySuccess, notifyError } from '@glidesystems/styleguide';
import {
    Method,
    PERMISSION,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
    batchRequestBody,
    buildExactQuery,
} from '@glidesystems/api';
import { selectAppReducer } from '../../../selectors';
import ActionButton from './ActionButton';
import { useNavigate } from 'react-router-dom';
import useToggle from '../../../hooks/useToggle';
import CreateIssueOrChange from '../../CreateIssueOrChange/CreateIssueOrChange';
import { Action } from './types';
import { Menu } from '@mui/material';
import get from 'lodash/get';
import AddRelations from '../../Relations/AddRelations';

const CreateCompletedOnTrackerAction = ({
    isMenuItem = false,
    detailEntityProp,
    onMenuItemClick,
    detailSchemaProp,
}: Action) => {
    const appSelector = useSelector(selectAppReducer);
    const detailEntity = detailEntityProp ?? appSelector.detailEntity;
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [completedOnTrackerForm, completedOnTrackerFormAction] = useToggle(false);
    const [addExisting, addExistingToggler] = useToggle(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const relationType = detailSchemaProp?.relationTypes?.find((rel) => rel.name === SYSTEM_RELATION.COMPLETED_ON);
    const findFormById = useDraftForm((state) => state.findFormById);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const onItemClick = (onClick) => {
        onClick && onClick();
        handleClose();
    };

    const handleOpenForm = useCallback((formId) => {
        const existingDraft = findFormById(formId);
        if (existingDraft) {
            dispatch(
                onOpenModal({
                    open: true,
                    title: existingDraft.title || 'Continue editing your draft form',
                    content: `${existingDraft.content}. Do you want to continue to <b>edit</b> or <b>discard</b> without saving?`,
                    cancelLabel: 'Discard',
                    confirmLabel: 'Edit your draft',
                    onModalNextAction: () => {
                        if (existingDraft.viewLocation) {
                            const { fullPath, query } = existingDraft.viewLocation;
                            navigate(fullPath + query);
                        }
                    },
                    onModalCancel: () => {
                        dismiss(formId);
                        completedOnTrackerFormAction.open();
                    },
                    action: 'confirm',
                })
            );
        } else {
            completedOnTrackerFormAction?.open();
        }
    }, []);

    const actionPermission = useMemo(() => {
        if (checkingPermission(detailEntity, null, PERMISSION.CAN_CONNECT_AS_FROM_SIDE, true)) {
            return {
                message: 'Create Completed on Tracker for this entity',
            };
        }
        return {
            error: true,
            message: `You don't have permission to create Completed on Tracker for this entity`,
        };
    }, [detailEntity]);

    const handleOpenAddExisting = () => {
        addExistingToggler.open();
    };

    const handleRefreshIssues = () => {
        if (detailEntity?.id) {
            dispatch(
                getIssueSummary(detailEntity.id, () =>
                    notifyError(
                        `An error occurred while refreshing the issue summary for ${get(detailEntity, [
                            'properties',
                            'name',
                        ])}`
                    )
                )
            );
        }
    };

    const onAddCompletedOnTracker = useCallback(
        async (
            properties: Record<string, any>,
            selectedRows: Array<Record<string, any>>,
            successCallback: () => void
        ) => {
            const params = selectedRows.map((row) => ({
                subParams: {
                    fromEntityId: detailEntity.id,
                    relationType: SYSTEM_RELATION.IMPLEMENTATION_TASK,
                    toEntityId: row.id,
                },
                body: {
                    properties: properties?.[row.id] ?? {},
                },
            }));

            const requests = batchRequestBody(Method.POST, `/entity/:fromEntityId/:relationType/:toEntityId`, params);
            const { data } = await batchRequest(requests);
            if (data.some((res) => !res.success || res.status != 200)) {
                notifyError(
                    `An error occurred while adding relations between selected entities and ${get(detailEntity, [
                        'properties',
                        'name',
                    ])}`
                );
            } else {
                notifySuccess(
                    `Successfully added relations between selected entities and <b>${get(detailEntity, [
                        'properties',
                        'name',
                    ])}</b>`
                );
                handleRefreshIssues();
                successCallback();
            }
        },
        [detailEntity]
    );

    return (
        <>
            {isMenuItem ? (
                <>
                    <ActionButton
                        isMenuItem
                        onClick={handleOpenForm}
                        Icon={PlusIcon}
                        actionPermission={actionPermission}
                        onMenuItemClick={onMenuItemClick}
                    >
                        Create New Completed On Tracker
                    </ActionButton>
                    <ActionButton
                        isMenuItem
                        onClick={handleOpenAddExisting}
                        Icon={LinkIcon}
                        actionPermission={actionPermission}
                        onMenuItemClick={onMenuItemClick}
                    >
                        Add to Bug
                    </ActionButton>
                </>
            ) : (
                <>
                    <ActionButton onClick={handleClick} actionPermission={actionPermission} Icon={PlusIcon}>
                        Completed On Tracker
                    </ActionButton>
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                        PaperProps={{
                            sx: {
                                minWidth: anchorEl ? anchorEl.clientWidth : 'auto',
                                mt: 1,
                            },
                        }}
                    >
                        <ActionButton
                            isMenuItem
                            onClick={() => onItemClick(handleOpenForm)}
                            actionPermission={actionPermission}
                            onMenuItemClick={handleClose}
                            Icon={PlusIcon}
                        >
                            Create New
                        </ActionButton>
                        <ActionButton
                            isMenuItem
                            onClick={() => onItemClick(handleOpenAddExisting)}
                            actionPermission={actionPermission}
                            onMenuItemClick={handleClose}
                            Icon={LinkIcon}
                        >
                            Add Existing
                        </ActionButton>
                    </Menu>
                </>
            )}

            <CreateIssueOrChange
                onOpen={completedOnTrackerFormAction.open}
                open={completedOnTrackerForm}
                onClose={completedOnTrackerFormAction.close}
                formId={SYSTEM_ENTITY_TYPE.COMPLETED_ON_TRACKER}
                type={SYSTEM_ENTITY_TYPE.COMPLETED_ON_TRACKER}
                detailEntityProp={detailEntityProp}
            />
            <AddRelations
                title={`Link Existing Completed Tracker(s)`}
                open={addExisting}
                onClose={addExistingToggler.close}
                entityType={SYSTEM_ENTITY_TYPE.COMPLETED_ON_TRACKER}
                extraFilters={buildExactQuery('permissions', PERMISSION.CAN_CONNECT_AS_TO_SIDE)}
                onSubmit={onAddCompletedOnTracker}
                includeRevision={false}
                draggable={false}
                placeholder="Search for Completed on Tracker..."
                relationSchema={{
                    relationType: relationType,
                }}
            />
        </>
    );
};

export default CreateCompletedOnTrackerAction;
