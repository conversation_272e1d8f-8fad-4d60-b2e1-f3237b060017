/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import Box from '@mui/material/Box';
import { LockAction, DeleteAction, CreateCompletedOnTrackerAction } from '../Actions';
import TabletMenu from '../Actions/TabletMenu';
import OSLCAction from '../Actions/OSLCAction';
import { EntityTypeActionsProps } from './types';
import ExportAction from '../Actions/ExportAction';
import useLargeScreen from '../../../hooks/useLargeScreen';

/**
 * DO NOT REMOVE OSLCAction as it provides 3rd party to integrate with our UI application
 */
const ITEMS = [
    {
        id: 'CompletedOnTrackerAction',
        Component: CreateCompletedOnTrackerAction,
    },
    {
        id: 'LockAction',
        Component: LockAction,
    },
    {
        id: 'DeleteAction',
        Component: DeleteAction,
    },
    { id: 'ExportAction', Component: ExportAction },
    {
        id: 'oslc-link-btn',
        Component: OSLCAction,
    },
];

const BugActions = ({ showTabletMenu = false, detailEntity, entitySchema }: EntityTypeActionsProps) => {
    const isLargeScreen = useLargeScreen();
    return isLargeScreen && !showTabletMenu ? (
        <Box
            sx={{
                display: 'flex',
                gap: '8px',
                justifyContent: 'flex-end',
                ml: '16px',
            }}
        >
            {ITEMS.map(({ id, Component }) => (
                <Component key={id} detailEntityProp={detailEntity} detailSchemaProp={entitySchema} />
            ))}
        </Box>
    ) : (
        <Box
            sx={{
                pt: '2px',
                display: 'flex',
                width: '50px',
                gap: '8px',
                justifyContent: 'flex-end',
            }}
        >
            <TabletMenu items={ITEMS} detailEntityProp={detailEntity} detailSchemaProp={entitySchema} />
        </Box>
    );
};

export default BugActions;
