/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { batchRequest } from '../../actions';
import { PlusIcon, notify<PERSON><PERSON><PERSON>, notifyError, Loading, AnimatedPage } from '@glidesystems/styleguide';
import {
    batchRequestBody,
    buildAndOperatorQuery,
    buildExactQuery,
    EntityDetail,
    entityUrls,
    fetch,
    Method,
    PERMISSION,
    SYSTEM_ENTITY_TYPE,
    buildlessThanOrEqualQuery,
    SYSTEM_RELATION,
} from '@glidesystems/api';
import { selectAppReducer } from '../../selectors';
import useToggle from '../../hooks/useToggle';
import { Action } from '../Navigation/Actions/types';
import { Box, Typography, styled, Button } from '@mui/material';
import AddRelations from '../Relations/AddRelations';
import { RelateOnItem } from './RelationAttribute';
import { CUSTOM_EVENTS } from '../../constants/events';

const Container = styled(Box)(({ theme }) => ({
    padding: '16px 12px',
    backgroundColor: theme.palette.glide.background.normal.blue2,
    display: 'flex',
    flexDirection: 'column',
    minHeight: '80px',
    marginBottom: '16px',
}));

const CreateIntroducedInAction = ({ detailEntityProp, detailSchemaProp }: Action) => {
    const appSelector = useSelector(selectAppReducer);
    const detailEntity = detailEntityProp ?? appSelector.detailEntity;
    const [reportedOn, setReportedOn] = useState(null);
    const [introducedIn, setIntroducedIn] = useState(null);
    const [loaded, setLoaded] = useState(false);
    const [addExisting, addExistingToggler] = useToggle(false);
    const relationType = detailSchemaProp?.relationTypes?.find(
        (rel) => rel.name === SYSTEM_RELATION.INTRODUCED_IN && rel.fromEntityType === SYSTEM_ENTITY_TYPE.BUG
    );

    const onItemClick = (onClick) => {
        onClick && onClick();
    };

    const handleOpenAddExisting = () => {
        addExistingToggler.open();
    };

    const fetchReportedOnAndIntroducedIn = useCallback(async (detailEntity: EntityDetail) => {
        setLoaded(false);
        try {
            const { data } = await fetch({
                ...entityUrls.getAllRelationsUnderEntity,
                params: {
                    entityId: detailEntity.id,
                },
                qs: {
                    relationNames: [SYSTEM_RELATION.REPORTED_ON, SYSTEM_RELATION.INTRODUCED_IN],
                    limit: 2,
                },
            });
            setReportedOn(data?.data?.find((rel) => rel.name === SYSTEM_RELATION.REPORTED_ON));
            setIntroducedIn(data?.data?.find((rel) => rel.name === SYSTEM_RELATION.INTRODUCED_IN));
            window.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.REFRESH_BUG_REVISION_SUMMARY));
        } catch (err) {
            console.log('Error fetching introduced in and reported on: ' + err);
            notifyError('There was an error fetching the Reported On And Introduced In Relations');
        } finally {
            setLoaded(true);
        }
    }, []);

    const refreshReportedOn = useCallback(async (detailEntity: EntityDetail) => {
        setLoaded(false);
        try {
            const { data } = await fetch({
                ...entityUrls.getAllRelationsUnderEntity,
                params: {
                    entityId: detailEntity.id,
                },
                qs: {
                    relationNames: [SYSTEM_RELATION.REPORTED_ON],
                    limit: 1,
                },
            });
            const refetchedReportedOn = data?.data?.find((rel) => rel.name === SYSTEM_RELATION.REPORTED_ON);
            setReportedOn(refetchedReportedOn);
            window.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.REFRESH_BUG_REVISION_SUMMARY));

            return refetchedReportedOn;
        } catch (err) {
            console.log('Error refreshing reported on: ' + err);
            notifyError('There was an error refreshing the Reported On Relationship');
        } finally {
            setLoaded(true);
        }
    }, []);

    const onAddIntroducedIn = useCallback(
        async (
            properties: Record<string, any>,
            selectedRows: Array<Record<string, any>>,
            successCallback: () => void
        ) => {
            const params = selectedRows.map((row) => ({
                subParams: {
                    fromEntityId: detailEntity.id,
                    toEntityId: row.id,
                    relationType: SYSTEM_RELATION.INTRODUCED_IN,
                },
                body: {
                    properties: properties?.[row.id] ?? {},
                },
            }));

            const requests = batchRequestBody(Method.POST, `/entity/:fromEntityId/:relationType/:toEntityId`, params);
            const { data } = await batchRequest(requests);
            if (data.some((res) => !res.success || res.status != 200)) {
                notifyError(`An error occurred while creating ${SYSTEM_RELATION.INTRODUCED_IN}`);
            } else {
                notifySuccess(`Successfully added ${SYSTEM_RELATION.INTRODUCED_IN}`);
                successCallback();
                await fetchReportedOnAndIntroducedIn(detailEntity);
            }
        },
        [detailEntity]
    );

    useEffect(() => {
        if (detailEntity?.id) {
            fetchReportedOnAndIntroducedIn(detailEntity);
        }
    }, [detailEntity]);

    if (!loaded) {
        return (
            <Container sx={{ justifyContent: 'center', alignItems: 'center', minHeight: '102px' }}>
                <Loading sxProps={{ height: '40px' }} />
            </Container>
        );
    }

    if (!reportedOn) {
        return <span>Cannot assign Introduced In Without Reported On</span>;
    }

    return (
        <>
            <RelateOnItem
                detailEntity={detailEntity}
                relationResponse={reportedOn}
                onEdit={() => {
                    refreshReportedOn(detailEntity).then((newReportedOn) => {
                        if (
                            newReportedOn.relation.name !== introducedIn.relation.name ||
                            newReportedOn.relation.createdAt < introducedIn.relation.createdAt
                        ) {
                            fetch({
                                ...entityUrls.deleteRelation,
                                params: {
                                    fromEntityId: detailEntity.id,
                                    toEntityId: introducedIn.relation.id,
                                    relationType: introducedIn?.name,
                                },
                                successMessage: `Successfully deleted Introduced In as Reported On was changed`,
                            }).then(() => {
                                fetchReportedOnAndIntroducedIn(detailEntity);
                            });
                        }
                    });
                }}
            />
            {introducedIn ? (
                <RelateOnItem
                    detailEntity={detailEntity}
                    relationResponse={introducedIn}
                    additionalQuery={buildAndOperatorQuery([
                        buildExactQuery('permissions', PERMISSION.CAN_CONNECT_AS_TO_SIDE),
                        buildExactQuery('name', reportedOn?.relation?.name),
                        buildlessThanOrEqualQuery('createdAt', reportedOn?.relation?.createdAt),
                    ])}
                />
            ) : (
                <Container
                    sx={{
                        backgroundColor: (theme) => theme.palette.glide.background.normal.blue2,
                        display: 'flex',
                        flexDirection: 'column',
                        minHeight: '102px',
                        py: 0,
                    }}
                >
                    <AnimatedPage>
                        <Box sx={{ display: 'flex', gap: '4px', flexDirection: 'column' }}>
                            <p>
                                <Typography
                                    sx={{
                                        fontSize: '14px',
                                        color: (theme) => theme.palette.glide.text.tertiary,
                                        lineHeight: '18px',
                                        gap: '8px',
                                        display: 'inline',
                                    }}
                                >
                                    <span style={{ fontWeight: 600 }}>Introduced In </span>has not been set
                                </Typography>{' '}
                            </p>
                            <Button
                                onClick={() => onItemClick(handleOpenAddExisting)}
                                variant="contained-blue"
                                sx={{ width: 'fit-content', height: '28px' }}
                                endIcon={<PlusIcon />}
                            >
                                Add
                            </Button>
                        </Box>
                        <AddRelations
                            title={`Add Introduced In`}
                            open={addExisting}
                            onClose={addExistingToggler.close}
                            entityType={SYSTEM_ENTITY_TYPE.PRODUCT}
                            extraFilters={buildAndOperatorQuery([
                                buildExactQuery('permissions', PERMISSION.CAN_CONNECT_AS_TO_SIDE),
                                buildExactQuery('name', reportedOn?.relation?.name),
                                buildlessThanOrEqualQuery('createdAt', reportedOn?.relation?.createdAt),
                            ])}
                            onSubmit={onAddIntroducedIn}
                            includeRevision={true}
                            draggable={false}
                            placeholder="Enter Revision Number..."
                            rowSelection="single"
                            relationSchema={{
                                relationType: relationType,
                            }}
                            defaultAttributesToSearch={['revision']}
                        />
                    </AnimatedPage>
                </Container>
            )}
        </>
    );
};

export default CreateIntroducedInAction;
