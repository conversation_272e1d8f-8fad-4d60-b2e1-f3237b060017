/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Box,
    Button,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    IconButton,
    styled,
    Typography,
} from '@mui/material';
import { Attribute } from '@glidesystems/api';
import { CloseIcon, EditIcon } from '@glidesystems/styleguide';
import { useState } from 'react';
import { CascadingList } from '../components/AttributeDetail/CascadingList';

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiPaper-root': {
        borderRadius: 0,
        width: '30%',
        height: '50%',
        minHeight: '500px',
        overflow: 'hidden',
        maxWidth: '100%',
    },
    '& .dialogHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        padding: '8px 8px 8px 24px',
        height: '46px',
        alignItems: 'center',
        backgroundColor: theme.palette.glide.background.normal.tertiary,
        '& .title': {
            fontSize: '20px',
            lineHeight: '150%',
            fontWeight: 600,
            color: theme.palette.glide.text.white,
            margin: 0,
        },
        '& .subTitle': {
            color: theme.palette.glide.text.normal.tertiary,
            fontWeight: 400,
            fontSize: '12px',
        },
        '& .headerButton': {
            alignSelf: 'flex-start',
            color: theme.palette.glide.text.normal.tertiary,
        },
    },
    '& .cancelBtn': {
        width: '160px',
        justifyContent: 'flex-start',
    },
    '& .searchBtn': {
        width: '260px',
        justifyContent: 'flex-start',
    },
    '& .actions': {
        borderTop: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    },
    '& .dialogContent': {
        margin: '24px 0',
        '& form': {
            '& .MuiPaper-root': {
                minWidth: '180px',
                width: 'max-content',
            },
        },
    },
    '& .MuiOutlinedInput-root': {
        height: 'min-content',
    },
}));

interface CascadingListEditableRendererProps {
    attribute: Attribute;
    value: string[];
    onChange: (value: string[]) => void;
    initialDialogState?: boolean;
}
export const CascadingListEditableRenderer = ({
    attribute,
    value,
    onChange,
    initialDialogState = false,
}: CascadingListEditableRendererProps) => {
    const [open, setOpen] = useState(initialDialogState);
    return (
        <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                    {value?.map((item, index) => (
                        <Chip key={`item-${index}`} label={item} />
                    ))}
                </div>
                <IconButton
                    onClick={() => {
                        setOpen(true);
                    }}
                >
                    <EditIcon />
                </IconButton>
            </Box>
            <StyledDialog
                open={open}
                onClose={(e, reason) => {
                    if (reason === 'backdropClick') {
                        setOpen(false);
                    }
                }}
            >
                <DialogTitle className="dialogHeader">
                    <Typography className="title">Edit {attribute?.displayName}</Typography>
                    <IconButton
                        className="headerButton"
                        onClick={() => {
                            setOpen(false);
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent className="dialogContent">
                    <Box sx={{ height: '90%' }}>
                        <CascadingList
                            value={value}
                            onChange={onChange}
                            cascadingListItems={attribute?.constraint?.cascadingListItems}
                            displayName={attribute?.displayName}
                        />
                    </Box>
                </DialogContent>
                <DialogActions sx={{ alignItems: 'center', px: '24px', my: '8px' }}>
                    <>
                        <Button
                            sx={{ minWidth: 160, justifyContent: 'flex-start' }}
                            onClick={() => onChange([])}
                            size="large"
                            variant="contained"
                            color="secondary"
                        >
                            Reset
                        </Button>
                        <Button
                            sx={{ minWidth: 226, justifyContent: 'flex-start' }}
                            onClick={() => setOpen(false)}
                            size="large"
                            variant="contained"
                            color={'info'}
                        >
                            Save
                        </Button>
                    </>
                </DialogActions>
            </StyledDialog>
        </>
    );
};
