{"name": "@glidesystems/api", "version": "1.0.0", "scripts": {"start": "concurrently pnpm:serve pnpm:emit-types", "serve": "rspack serve --port 9007", "emit-types": "tsc --watch --emitDeclarationOnly", "start:standalone": "concurrently pnpm:emit-types rspack serve --env standalone", "build": "concurrently pnpm:build:*", "build:rspack": "rspack --mode=production", "analyze": "rspack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "clean": "rm -rf node_modules"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "babel-jest": "^27.0.6", "@rspack/cli": "^1.2.3", "@rspack/core": "^1.2.3", "@types/node": "^24.2.0", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "keycloak-js": "^26.2.0", "lint-staged": "^13.0.2", "ts-config-single-spa": "^3.0.0", "typescript": "5.7.3", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@tanstack/query-core": "^5.72.1", "@glidesystems/styleguide": "workspace:*", "@types/jest": "^27.0.1", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/systemjs": "^6.1.1", "axios": "^1.9.0", "date-fns": "^2.29.2", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "query-string": "^7.1.1", "react": "^18.3.0", "react-dom": "^18.3.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1"}, "types": "types/glidesystems-api.d.ts"}