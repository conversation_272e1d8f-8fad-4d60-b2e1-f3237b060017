/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method, type UrlGroup } from '../../services/fetch';

const BASE_URL = `${process.env.BASE_URL}/entity`;

const entityUrls = {
    getListEntity: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType`,
    },
    getEntityById: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType/:entityId`,
    },
    getEntityAccesses: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/access`,
    },
    createEntity: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityType`,
    },
    updateEntity: {
        method: Method.PUT,
        url: `${BASE_URL}/entity/:entityId`,
    },
    deleteEntity: {
        method: Method.DELETE,
        url: `${BASE_URL}/entity/:entityId`,
    },
    getEntityRelations: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:fromEntityId/:relationType/:entityType`,
    },
    createRelation: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:fromEntityId/:relationType/:toEntityId`,
    },
    deleteRelation: {
        method: Method.DELETE,
        url: `${BASE_URL}/entity/:fromEntityId/:relationType/:toEntityId`,
    },
    deleteRelationByRelationId: {
        method: Method.DELETE,
        url: `${BASE_URL}/entity/:fromEntityId/relation/:relationId`,
    },
    getListEntitySysRoot: {
        method: Method.GET,
        url: `${BASE_URL}/entity/SysRoot`,
    },
    getRelationsUnderEntity: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType/:entityId/relation`,
    },
    getAllRelationsUnderEntity: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/relation`,
    },
    getBOMList: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/bom`,
    },
    lockEntity: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/lock`,
    },
    unlockEntity: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/unlock`,
    },
    grantPermission: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/grant/:stateName`,
    },
    revokePermission: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/revoke/:stateName`,
    },
    whereUsed: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/usedIn`,
    },
    batchRequest: {
        method: Method.POST,
        url: `${process.env.BASE_URL}/entity/batch`,
    },
    swapBOM: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:bomId/swap/:toComponentId`,
    },
    applyClassifications: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/classification`,
    },
    updateRelation: {
        method: Method.PUT,
        url: `${BASE_URL}/entity/:fromEntityId/relation/:relationId`,
    },
    replaceRelation: {
        method: Method.PUT,
        url: `${BASE_URL}/entity/:fromEntityId/:relationName/:toEntityId`,
    },
    promote: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/promote/:stateName`,
    },
    demote: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/demote/:stateName`,
    },
    revise: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/revise`,
    },
    generateEBom: {
        method: Method.POST,
        url: `${BASE_URL}/entity/:entityId/convert-ebom`,
    },
    getManufacturerParts: {
        method: Method.GET,
        url: `${BASE_URL}/part/:entityId/manufacturer`,
    },
    createDiscussion: {
        method: Method.POST,
        url: `${BASE_URL}/discussion`,
    },
    getDiscussions: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/discussion`,
    },
    getDiscussionMessages: {
        method: Method.GET,
        url: `${BASE_URL}/discussion/:id/message`,
    },
    deleteDiscussion: {
        method: Method.DELETE,
        url: `${BASE_URL}/discussion/:id`,
    },
    addMessageToDiscussion: {
        method: Method.POST,
        url: `${BASE_URL}/discussion/:id/message`,
    },
    replyMessage: {
        method: Method.POST,
        url: `${BASE_URL}/discussion/:discussionId/message/:messageId/reply`,
    },
    deleteMessage: {
        method: Method.DELETE,
        url: `${BASE_URL}/discussion/:discussionId/message/:messageId`,
    },
    getRollup: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType/:entityId/rollup/:bugEntityType`,
    },
    getImpactAnalysis: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType/:entityId/impactAnalysis`,
    },
    getBugRevisionSummary: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityType/:entityId/bugRevisionSummary`,
    },
    getAllEventType: {
        method: Method.GET,
        url: `${BASE_URL}/entity/event-type`,
    },
    createDecomposition: {
        method: Method.POST,
        url: `${process.env.BASE_URL}/decomposition/:decompositionType`,
    },
} as const;

export default entityUrls;
