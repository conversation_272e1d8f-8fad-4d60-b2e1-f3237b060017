/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export enum ExportType {
    EXCEL = 'EXCEL',
    CSV = 'CSV',
    PDX = 'PDX',
}

export const EXPORT_TYPE_TO_EXTENSION = {
    [ExportType.CSV]: '.csv',
    [ExportType.EXCEL]: '.xls',
    [ExportType.PDX]: '.xml',
} as const;

export enum ExportLayout {
    TABLE_LAYOUT = 'TABLE_LAYOUT',
    SINGLE_ENTITY_LAYOUT = 'SINGLE_ENTITY_LAYOUT',
}

export const EXPORT_EXTENSION_TO_TYPE = {
    ['.csv']: ExportType.CSV,
    ['.xls']: ExportType.EXCEL,
    ['.xml']: ExportType.PDX,
};

export const OTHERS_EXPORT_GROUP_LABEL = 'Others';
