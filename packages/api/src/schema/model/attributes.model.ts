/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { type AttributeConstraint } from './attribute-constraint.model';
import AttributeType from './attribute-type.enum';

interface Identifier {
    nextAssignment: string;
    prefix: string;
    skipCharacters: string;
    suffix: string;
}

export default interface Attribute {
    id: string;
    updateAt: string;
    createdAt: string;
    disabled: boolean;
    constraint?: AttributeConstraint;
    description: string;
    name: string;
    displayName: string;
    nullable: boolean;
    system: boolean;
    type: AttributeType;
    defaultValue: any;
    visible: boolean;
    identifier?: Identifier;
    hyperlink: boolean;
    mutable: boolean;
    richText: boolean;
    unitOfMeasure?: {
        quantityKind: string;
        quantityUnit: string;
    };
}

export interface AttributeGroupWithValue extends Attribute {
    quantityUnit?: string;
    type: Exclude<AttributeType, AttributeType.GROUP>;
    value: any;
}

export interface AttributeGroup {
    type: AttributeType.GROUP;
    name: string;
    attributes: AttributeGroupWithValue[];
}

export type AttributeOrGroup = AttributeGroup | AttributeGroupWithValue;
