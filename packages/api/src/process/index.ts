/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import { Method } from '../services/fetch';

const PROCESS_BASE_URL = `${process.env.BASE_URL}/process/process`;
const PROCESS_ENGINE_BASE_URL = `${process.env.BASE_URL}/engine/engine-rest`;

const processUrls = {
    getTasks: {
        method: Method.GET,
        url: `${PROCESS_BASE_URL}/tasks`,
    },
    getEngineUserTasks: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task`,
    },
    createTasks: {
        method: Method.POST,
        url: `${PROCESS_BASE_URL}/tasks`,
    },
    getProcessInstances: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/process-instance`,
    },
    getHistoryProcessInstances: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/history/process-instance`,
    },
    getHistoryProcessActivityInstances: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/history/activity-instance`,
    },
    getProcessInstanceActivities: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/process-instance/:processInstanceId/activity-instances`,
    },
    getProcessDefinition: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/process-definition/:definitionId/xml`,
    },
    getProcessInstanceIncidents: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/incident`,
    },
    getProcessTaskDetails: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId`,
    },
    getProcessTaskVariables: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/variables`,
    },
    completeTask: {
        method: Method.POST,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/complete`,
    },
    queryTask: {
        method: Method.POST,
        url: `${PROCESS_ENGINE_BASE_URL}/task`,
    },
    updateTask: {
        method: Method.PUT,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId`,
    },
    getTaskComments: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/comment`,
    },
    addTaskComments: {
        method: Method.POST,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/comment/create`,
    },
    getHistoryUserOperation: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/history/user-operation`,
    },
    getTaskAttachments: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/attachment`,
    },
    createTaskAttachment: {
        method: Method.POST,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/attachment/create`,
    },
    downloadAttachment: {
        method: Method.GET,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/attachment/:attachmentId/data`,
    },
    deleteAttachment: {
        method: Method.DELETE,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/attachment/:attachmentId`,
    },
    claimTask: {
        method: Method.POST,
        url: `${PROCESS_ENGINE_BASE_URL}/task/:taskId/claim`,
    },
    setRetries: {
        method: Method.PUT,
        url: `${PROCESS_ENGINE_BASE_URL}/external-task/:taskId/retries`,
    },
} as const;

export default processUrls;

export const buildAttachmentForm = (file, description?) => {
    const formData = new FormData();
    const extension = file && get(file.name?.split('.'), [1]);
    formData.append('content', file);
    formData.append('attachment-name', file.name);
    formData.append('attachment-type', extension);
    formData.append('attachment-description', description);
    return formData;
};
