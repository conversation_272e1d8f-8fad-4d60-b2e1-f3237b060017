/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from '../services/fetch';

const BASE_URL = `${process.env.BASE_URL}/change-manager`;

const changeUrls = {
    getIssueSummary: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityId/issue-summary`,
    },
    getRevisionIssueSummary: {
        method: Method.GET,
        url: `${BASE_URL}/entity/:entityMasterId/revision-summary`,
    },
    updateAttributePlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/update-attribute`,
    },
    addComponentPlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/add-component`,
    },
    deleteComponentPlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/delete-component`,
    },
    addContextualAlternatePlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/add-contextual-alternate`,
    },
    addGlobalAlternatePlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/add-global-alternate`,
    },
    removeContextualAlternatePlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/remove-contextual-alternate`,
    },
    removeGlobalAlternatePlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/remove-global-alternate`,
    },
    replaceComponentPlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/replace-component`,
    },
    swapComponentPlan: {
        method: Method.POST,
        url: `${BASE_URL}/planned-change/:entityId/swap-component`,
    },
    getChangeRequestAffectedItems: {
        method: Method.GET,
        url: `${BASE_URL}/change-request/:entityId/affected-item`,
    },
    getChangeOrderAffectedItems: {
        method: Method.GET,
        url: `${BASE_URL}/change-order/:entityId/affected-item`,
    },
    getPlannedChangeBomSummary: {
        method: Method.GET,
        url: `${BASE_URL}/planned-change/:entityId/bom-change-summary`,
    },
    createChangeOrder: {
        method: Method.POST,
        url: `${BASE_URL}/change-order`,
    },
} as const;

export default changeUrls;
