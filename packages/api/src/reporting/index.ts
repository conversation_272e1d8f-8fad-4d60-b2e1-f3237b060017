/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from '../services/fetch';

const BASE_URL = `${process.env.BASE_URL}/reporting`;

const reportingUrls = {
    getReports: {
        method: Method.GET,
        url: `${BASE_URL}/report`,
    },
    createReport: {
        method: Method.POST,
        url: `${BASE_URL}/report`,
    },
    getReportDetail: {
        method: Method.GET,
        url: `${BASE_URL}/report/:reportId`,
    },
    getReportExecutions: {
        method: Method.GET,
        url: `${BASE_URL}/report/:reportId/execution`,
    },
    updateReport: {
        method: Method.PUT,
        url: `${BASE_URL}/report/:reportId`,
    },
    deleteReport: {
        method: Method.DELETE,
        url: `${BASE_URL}/report/:reportId`,
    },
    getReportLayouts: {
        method: Method.GET,
        url: `${BASE_URL}/report/:reportId/report-layout`,
    },
    createReportLayout: {
        method: Method.POST,
        url: `${BASE_URL}/report/:reportId/report-layout`,
    },
    getReportSchedules: {
        method: Method.GET,
        url: `${BASE_URL}/report/:reportId/schedule`,
    },
    createReportSchedule: {
        method: Method.POST,
        url: `${BASE_URL}/report/:reportId/schedule`,
    },
    updateReportSchedule: {
        method: Method.PUT,
        url: `${BASE_URL}/report/:reportId/schedule/:scheduleId`,
    },
    deleteReportSchedule: {
        method: Method.DELETE,
        url: `${BASE_URL}/report/:reportId/schedule/:scheduleId`,
    },
    disableReportSchedule: {
        method: Method.POST,
        url: `${BASE_URL}/report/:reportId/schedule/:scheduleId/disable`,
    },
    enableReportSchedule: {
        method: Method.POST,
        url: `${BASE_URL}/report/:reportId/schedule/:scheduleId/enable`,
    },
    getReportLayoutDetail: {
        method: Method.GET,
        url: `${BASE_URL}/report-layout/:reportLayoutId`,
    },
    updateReportLayout: {
        method: Method.PUT,
        url: `${BASE_URL}/report-layout/:reportLayoutId`,
    },
    deleteReportLayout: {
        method: Method.DELETE,
        url: `${BASE_URL}/report-layout/:reportLayoutId`,
    },
} as const;

export default reportingUrls;
