/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { stringify } from 'query-string';
import get from 'lodash/get';
import { navigateToUrl } from 'single-spa';
import axiosInstance from './api_interceptor';
import { notify<PERSON>uc<PERSON>, notifyError, notifyBusinessRuleErrors } from '@glidesystems/styleguide';
import axios, { ResponseType, type RawAxiosRequestHeaders, AxiosResponse } from 'axios';
import { authenticationService, RESPONSE_ERROR_TYPE, ResponseError } from '../glidesystems-api';
import { FetchQueryOptions, QueryClient, StaleTime } from '@tanstack/query-core';

export enum Method {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
    OPTIONS = 'OPTIONS',
}

export interface UrlFormat {
    method: Method;
    url: string;
}

export type UrlGroup = Record<string, UrlFormat>;

export const invariant = (cond: boolean, message: string) => {
    if (!cond) throw new Error(message);
};

export const generatePath = (path: string, params: Record<string, any>): string => {
    return path
        .replace(/:(\w+)/g, (_, key) => {
            invariant(params[key] != null, `Missing ":${key}" param`);
            return params[key]!;
        })
        .replace(/\/*\*$/, (_) => (params['*'] == null ? '' : params['*'].replace(/^\/*/, '/')));
};

interface FetchProps {
    url: string;
    params?: Record<string, any>;
    method: Method;
    qs?: Record<string, any>;
    data?: Record<string, any>;
    successMessage?: string | JSX.Element;
    errorMessage?: string | JSX.Element;
    shouldShow404?: boolean;
    shouldShow403?: boolean;
    skipToast?: boolean;
    skipDetails?: boolean;
    headers?: RawAxiosRequestHeaders;
    signal?: AbortSignal;
    responseType?: ResponseType;
    fetchQueryOptions?: Partial<FetchQueryOptions>;
    onUploadProgress?: (progressEvent: any) => void;
}

const generateQueryKey = (url: string, params: any, qs: any) => {
    return [url, JSON.stringify(params || {}), JSON.stringify(qs || {})];
};

export const queryClient = new QueryClient();
export const fetch = async ({
    url,
    params,
    method = Method.GET,
    qs,
    data,
    successMessage,
    errorMessage,
    shouldShow404 = false,
    shouldShow403 = false,
    skipToast = false,
    skipDetails = false,
    headers,
    signal,
    responseType = 'json',
    fetchQueryOptions = {},
    onUploadProgress = () => {},
}: FetchProps): Promise<AxiosResponse<any>> => {
    const path = generatePath(url, params);
    const fullUrl = qs ? `${path}?${stringify(qs)}` : path;

    const request = async () => {
        const response = await axiosInstance({
            data,
            method,
            url: fullUrl,
            headers,
            signal,
            responseType: responseType as any,
            onUploadProgress,
        });

        if (successMessage) notifySuccess(successMessage);
        return response;
    };

    try {
        if (method.toUpperCase() === 'GET') {
            const queryKey = generateQueryKey(url, params, qs);
            return (await queryClient.fetchQuery({
                queryKey,
                queryFn: request,
                staleTime: 0, // Default no-cache
                ...fetchQueryOptions,
            })) as Promise<AxiosResponse<any>>;
        }
        return await request();
    } catch (err: any) {
        const status = err.response?.status;
        const isInvalidAttributeError = err.response?.data?.errors?.some(
            (e: ResponseError) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
        );
        const isBusinessRuleError = err.response?.data?.businessRuleResults?.length > 0;

        if (status === 400 && get(err?.response, ['data', 'metadata', 'rule'], []).length > 0) {
            notifyBusinessRuleErrors(get(err.response, ['data', 'metadata', 'rule'], []), { skipDetails });
            throw err;
        }

        if (status === 400 && (isInvalidAttributeError || isBusinessRuleError)) {
            throw err;
        }

        if (status === 403 && shouldShow403) {
            navigateToUrl(`${window.location.origin}/access-denied`);
            return;
        }

        if (status === 404 && shouldShow404) {
            navigateToUrl(`${window.location.origin}/not-found`);
            return;
        }

        if (status === 401) {
            authenticationService.login();
            return;
        }

        if (axios.isCancel(err)) {
            console.log('Request cancelled');
            return;
        }

        if (errorMessage && !skipToast) {
            notifyError(errorMessage);
        } else if (!skipToast) {
            const formatted = get(err, ['response', 'data', 'errorMessage'], err.message);
            notifyError(`Error: ${typeof formatted === 'string' ? formatted : JSON.stringify(formatted)}`);
        }

        throw err;
    }
};

export default fetch;
