/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from '../services/fetch';

const BASE_URL = `${process.env.BASE_URL}/migration`;

const migrationUrls = {
    import: {
        method: Method.POST,
        url: `${BASE_URL}/import`,
    },
    getImportJobs: {
        method: Method.GET,
        url: `${BASE_URL}/import`,
    },
    getImportDetails: {
        method: Method.GET,
        url: `${BASE_URL}/import/:id`,
    },
    getFailedImport: {
        method: Method.GET,
        url: `${BASE_URL}/import/:id/fail`,
    },
    getSuccessImport: {
        method: Method.GET,
        url: `${BASE_URL}/import/:id/success`,
    },
    export: {
        method: Method.POST,
        url: `${BASE_URL}/export`,
    },
    createExportTemplate: {
        method: Method.POST,
        url: `${BASE_URL}/export_template`,
    },
    updateExportTemplate: {
        method: Method.PUT,
        url: `${BASE_URL}/export_template/:id`,
    },
    deleteExportTemplate: {
        method: Method.DELETE,
        url: `${BASE_URL}/export_template/:id`,
    },
    getExportTemplates: {
        method: Method.GET,
        url: `${BASE_URL}/export_template`,
    },
    getExportTemplateDetail: {
        method: Method.GET,
        url: `${BASE_URL}/export_template/:id`,
    },
    createImportTemplate: {
        method: Method.POST,
        url: `${BASE_URL}/import_template`,
    },
    updateImportTemplate: {
        method: Method.PUT,
        url: `${BASE_URL}/import_template/:id`,
    },
    getImportTemplates: {
        method: Method.GET,
        url: `${BASE_URL}/import_template`,
    },
    downloadFailedRows: {
        method: Method.GET,
        url: `${BASE_URL}/import/:id/fail/download`,
    },
    downloadOriginalFile: {
        method: Method.GET,
        url: `${BASE_URL}/import/:id/download`,
    },
} as const;

export default migrationUrls;
