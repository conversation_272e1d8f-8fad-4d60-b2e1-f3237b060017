/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import fetch, { generatePath, Method } from '../services/fetch';
import chunk from 'lodash/chunk';
import get from 'lodash/get';
import flatten from 'lodash/flatten';
import { commonMessages, notify<PERSON>uc<PERSON>, notifyError } from '@glidesystems/styleguide';
import { DEFAULT_BATCH_CHUNK_SIZE } from '../glidesystems-api';
import { AxiosResponse } from 'axios';

type PartialExcept<T, K extends keyof T> = Partial<Omit<T, K>> & Pick<T, K>;

interface RequestData {
    subParams: Record<string, any>;
    body: any;
    url: string;
    method: Method;
}

interface RequestDataWithSubRequests extends RequestData {
    subRequests?: Partial<RequestData>[];
}

export type BatchRequestData = Partial<RequestDataWithSubRequests>;

export interface BatchRequestResponse<T = any> {
    success: boolean;
    data?: AxiosResponse<T, any>[];
    error?: any;
}

interface BatchRequest {
    url: string;
    method: Method;
    data: Array<Record<string, any>>;
    maxChunkRequest?: number;
}

interface BatchRequestWithProgress extends BatchRequest {
    onProgressChange?: (progress: number) => void;
    signal: AbortSignal;
}

export interface HandleBatchRequest<T> {
    response: BatchRequestResponse<T>;
    statusCodeOnSuccess?: number[];
    onSuccess?: () => void;
    onFailed?: () => void;
    successMessage?: string;
    failedMessage?: string;
}

const createBatchReqData = (method = Method.POST, url: string, data: BatchRequestData[]): BatchRequestData[] => {
    const reqData = data.map((item) => {
        const path = generatePath(item.url ?? url, item.subParams);
        return {
            method: item.method ?? method,
            url: path,
            body: item.body || {},
            subRequests:
                item.subRequests &&
                item.subRequests.map((subRequest) => ({
                    url: generatePath(subRequest.url ?? url, subRequest.subParams),
                    method: subRequest.method ?? method,
                    body: subRequest.body,
                })),
        };
    });
    return reqData;
};

export const batchRequest = async ({
    url,
    method,
    data,
    maxChunkRequest = DEFAULT_BATCH_CHUNK_SIZE,
}: BatchRequest): Promise<BatchRequestResponse> => {
    try {
        let chunks = [data];

        if (data.length > maxChunkRequest) {
            chunks = chunk(data, maxChunkRequest);
        }

        const res = await Promise.all(
            chunks.map((chunk) =>
                fetch({
                    url,
                    params: {},
                    method,
                    qs: {},
                    data: chunk,
                    successMessage: '',
                    errorMessage: '',
                    shouldShow404: false,
                    signal: null,
                })
            )
        );

        return {
            success: true,
            data: res,
        };
    } catch (error) {
        return {
            success: false,
            error,
        };
    }
};

export const batchWithProgress = async ({
    url,
    method,
    data,
    maxChunkRequest = DEFAULT_BATCH_CHUNK_SIZE,
    onProgressChange,
    signal = null,
}: BatchRequestWithProgress): Promise<BatchRequestResponse> => {
    try {
        let chunks = [data];

        if (data.length > maxChunkRequest) {
            chunks = chunk(data, maxChunkRequest);
        }

        const total = chunks.length;
        let progress = 0;
        let res = [];

        for (const chunk of chunks) {
            const data = await fetch({
                url,
                params: {},
                method,
                qs: {},
                data: chunk,
                successMessage: '',
                errorMessage: '',
                shouldShow404: false,
                signal,
            });
            res.push(data);
            progress += 100 / total;
            if (onProgressChange) {
                onProgressChange(progress);
            }
        }

        return {
            success: true,
            data: res,
        };
    } catch (error) {
        return {
            success: false,
            error,
        };
    }
};

export const handleBatchRequestRes = ({
    response,
    statusCodeOnSuccess = [201],
    onSuccess = () => {},
    onFailed = () => {},
    successMessage = commonMessages.successRequest,
    failedMessage = commonMessages.failedRequest,
}: HandleBatchRequest<any>): void => {
    const { success, data } = response;
    if (success && data && data.every((r: any) => r.status === 200)) {
        // Flatten the response data
        const resDetails = flatten(data.map((r: any) => r.data));
        let successReq = [];
        let failedReq = [];

        resDetails.forEach((d: any) => {
            if (statusCodeOnSuccess.includes(d.status)) successReq.push(d);
            else failedReq.push(d);
        });

        const failedReqs = resDetails.filter((d: any) => !statusCodeOnSuccess.includes(d.status));
        if (failedReqs.length === 0) {
            notifySuccess(successMessage);
            onSuccess();
        } else {
            notifyError(get(failedReqs[0], ['response', 'errorMessage'], failedReqs[0]));
            onFailed();
        }
    } else {
        notifyError(failedMessage);
        onFailed();
    }
};

export default createBatchReqData;
