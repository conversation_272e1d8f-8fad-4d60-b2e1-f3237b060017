/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { format } from 'date-fns';
import type Attribute from '../schema/model/attributes.model';

export const QUERY = {
    OR: '$or',
    AND: '$and',
    EXACT: '$exact',
    NOT_IN: '$not_in',
    IN: '$in',
    IS_NULL: '$is_null',
    IS_NON_NULL: '$is_non_null',
    LT: '$lt',
    LTE: '$lte',
    GT: '$gt',
    GTE: '$gte',
    REGEX: '$regex',
    CONTAINS: '$contains',
    NOT_CONTAINS: '$not_contains',
    EQUAL: '$eq',
    NOT: '$not',
    NOT_EQUAL: '$neq',
    BETWEEN: '$btw',
} as const;
export const QUERY_TYPES = Object.values(QUERY);
export const QUERY_OPERATOR_OPTIONS = [
    {
        label: 'Or',
        value: QUERY.OR,
    },
    {
        label: 'And',
        value: QUERY.AND,
    },
    {
        label: 'Contains',
        value: QUERY.CONTAINS,
    },
    {
        label: 'Exact',
        value: QUERY.EXACT,
    },
    {
        label: 'In',
        value: QUERY.IN,
    },
    {
        label: 'Not In',
        value: QUERY.NOT_IN,
    },
    {
        label: 'Is Null',
        value: QUERY.IS_NULL,
    },
    {
        label: 'Is not Null',
        value: QUERY.IS_NON_NULL,
    },
    {
        label: 'Less Than',
        value: QUERY.LT,
    },
    {
        label: 'Less Than or Equal',
        value: QUERY.LTE,
    },
    {
        label: 'Greater Than',
        value: QUERY.GT,
    },
    {
        label: 'Greater Than or Equal',
        value: QUERY.GTE,
    },
    {
        label: 'Regular Expression',
        value: QUERY.REGEX,
    },
    {
        label: 'Equal',
        value: QUERY.EQUAL,
    },
    {
        label: 'Not Equal',
        value: QUERY.NOT_EQUAL,
    },
] as const;
export const buildNotEqualOperatorQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.NOT_EQUAL]: { [field]: value },
    };
};

export const buildNotOperatorQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.NOT]: { [field]: value },
    };
};

export const buildEqualOperatorQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.EQUAL]: { [field]: value },
    };
};

export const buildOrOperatorQuery = (queryList): Record<string, any> => {
    return {
        [QUERY.OR]: queryList,
    };
};

export const buildAndOperatorQuery = (queryList): Record<string, any> => {
    return {
        [QUERY.AND]: queryList,
    };
};

export const buildExactQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.EXACT]: { [field]: value },
    };
};

export const buildNotInQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.NOT_IN]: { [field]: value },
    };
};

export const buildInQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.IN]: { [field]: value },
    };
};

export const buildNotNullQuery = (field): Record<string, any> => {
    return {
        [QUERY.IS_NON_NULL]: field,
    };
};

export const buildNullQuery = (field): Record<string, any> => {
    return {
        [QUERY.IS_NULL]: field,
    };
};

export const buildLessThanQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.LT]: { [field]: value },
    };
};

export const buildlessThanOrEqualQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.LTE]: { [field]: value },
    };
};

export const buildGreaterThanQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.GT]: { [field]: value },
    };
};

export const buildGreaterThanOrEqualQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.GTE]: { [field]: value },
    };
};

export const buildRegexQuery = (field, value): Record<string, any> => {
    return {
        [QUERY.REGEX]: { [field]: value },
    };
};

export const buildContainsQuery = (field: string, value: string): Record<string, any> => {
    if (value.includes('*')) {
        return buildRegexQuery(field, value);
    }
    return {
        [QUERY.CONTAINS]: {
            [field]: value,
        },
    };
};

const formatDate = (date: string) => format(new Date(date), 'yyyy-MM-dd');

export const getCriteriasQuery = (key, item) => {
    switch (item.type) {
        case 'contains':
            if (item.filter.includes('*') || item.filter.includes('?')) {
                return buildRegexQuery(key, item.filter);
            }
            return buildContainsQuery(key, item.filter);
        case 'equals': {
            if (item.filterType === 'date') {
                const dateFormated = formatDate(item.dateFrom);
                return buildExactQuery(key, dateFormated);
            }
            return buildExactQuery(key, item.filter);
        }
        case 'in':
            return buildInQuery(key, item.values);
        case 'notIn':
            return buildNotInQuery(key, item.values);
        case 'notBlank':
            return buildNotNullQuery(key);
        case 'blank':
            return buildNullQuery(key);
        case 'lessThan': {
            if (item.filterType === 'date') {
                const dateFormated = formatDate(item.dateFrom);
                return buildLessThanQuery(key, dateFormated);
            }
            return buildLessThanQuery(key, item.filter);
        }
        case 'lessThanOrEqual': {
            if (item.filterType === 'date') {
                const dateFormated = formatDate(item.dateFrom);
                return buildlessThanOrEqualQuery(key, dateFormated);
            }
            return buildlessThanOrEqualQuery(key, item.filter);
        }
        case 'greaterThan': {
            if (item.filterType === 'date') {
                const dateFormated = formatDate(item.dateFrom);
                return buildGreaterThanQuery(key, dateFormated);
            }
            return buildGreaterThanQuery(key, item.filter);
        }
        case 'greaterThanOrEqual': {
            if (item.filterType === 'date') {
                const dateFormated = formatDate(item.dateFrom);
                return buildGreaterThanOrEqualQuery(key, dateFormated);
            }
            return buildGreaterThanOrEqualQuery(key, item.filter);
        }
        default:
            console.log('unknown filter type');
            return;
    }
};

export const extractAndOrCriterias = (operation, condition) => {
    if (!operation || !condition) return;
    if (operation === 'AND') {
        return buildAndOperatorQuery(condition);
    } else {
        return buildOrOperatorQuery(condition);
    }
};

export interface QueryCondition {
    [QUERY.AND]: QueryCondition[];
    [QUERY.OR]: QueryCondition[];
    [QUERY.IN]: { [key: string]: string[] };
    [QUERY.NOT_IN]: { [key: string]: string[] };
    [QUERY.CONTAINS]: { [key: string]: string };
    [QUERY.EXACT]: { [key: string]: string };
    [QUERY.IS_NULL]: string;
    [QUERY.IS_NON_NULL]: string;
    [QUERY.LT]: { [key: string]: string };
    [QUERY.LTE]: { [key: string]: string };
    [QUERY.GT]: { [key: string]: string };
    [QUERY.GTE]: { [key: string]: string };
    [QUERY.REGEX]: { [key: string]: string };
    [QUERY.EQUAL]: { [key: string]: string };
    [QUERY.NOT_EQUAL]: { [key: string]: string };
    [QUERY.BETWEEN]: { [key: string]: string[] };
    [key: string]: any;
}

export const convertQuery = (queryString: string): string => {
    try {
        const query: QueryCondition = JSON.parse(queryString);
        const convertedString = convertQueryObject(query);
        if (convertedString?.startsWith('(')) {
            // strip first and last parantheses
            return convertedString?.substring(1, convertedString.length - 1);
        }
        return convertedString;
    } catch (error) {
        console.error('Invalid JSON string');
        return '';
    }
};

export const convertQueryObject = (query: Partial<QueryCondition>): string => {
    if (query[QUERY.AND]) {
        const conditions = query[QUERY.AND]
            .map((condition) => convertQueryObject(condition))
            .filter((condition) => condition && condition.trim() !== ''); // Filter out empty conditions

        if (conditions.length === 0) {
            return '';
        }
        if (conditions.length === 1) {
            return conditions[0];
        }
        return '(' + conditions.join(' AND ') + ' )';
    }

    if (query[QUERY.OR]) {
        const conditions = query[QUERY.OR]
            .map((condition) => convertQueryObject(condition))
            .filter((condition) => condition && condition.trim() !== ''); // Filter out empty conditions

        if (conditions.length === 0) {
            return '';
        }
        if (conditions.length === 1) {
            return conditions[0];
        }
        return '(' + conditions.join(' OR ') + ' )';
    }

    if (query[QUERY.EQUAL]) {
        const [field, value] = Object.entries(query[QUERY.EQUAL])[0];
        return `${field} EQUALS "${value ?? ''}"`;
    }

    if (query[QUERY.NOT_EQUAL]) {
        const [field, value] = Object.entries(query[QUERY.NOT_EQUAL])[0];
        return `${field} NOT EQUALS "${value ?? ''}"`;
    }

    if (query[QUERY.IS_NULL]) {
        const field = query[QUERY.IS_NULL];
        return `${field} IS NULL`;
    }

    if (query[QUERY.IS_NON_NULL]) {
        const field = query[QUERY.IS_NON_NULL];
        if (field.startsWith('relation') && field.endsWith('.id')) {
            return `${field.replace(/\.id$/, '')} EXISTS`;
        }
        return `${field} IS NOT NULL`;
    }

    if (query[QUERY.IN]) {
        const [field, values] = Object.entries(query[QUERY.IN])[0];
        return `${field} IN (${values.map((v) => `"${v}"`).join(', ')})`;
    }

    if (query[QUERY.NOT_IN]) {
        const [field, values] = Object.entries(query[QUERY.NOT_IN])[0];
        return `${field} NOT IN (${values.map((v) => `"${v}"`).join(', ')})`;
    }

    if (query[QUERY.CONTAINS]) {
        const [field, value] = Object.entries(query[QUERY.CONTAINS])[0];
        return `${field} CONTAINS "${value ?? ''}"`;
    }

    if (query[QUERY.EXACT]) {
        const [field, value] = Object.entries(query[QUERY.EXACT])[0];
        return `${field} EQUALS "${value ?? ''}"`;
    }

    if (query[QUERY.GT]) {
        const [field, value] = Object.entries(query[QUERY.GT])[0];
        return `${field} > "${value}"`;
    }

    if (query[QUERY.GTE]) {
        const [field, value] = Object.entries(query[QUERY.GTE])[0];
        return `${field} >= "${value}"`;
    }

    if (query[QUERY.LT]) {
        const [field, value] = Object.entries(query[QUERY.LT])[0];
        return `${field} < "${value}"`;
    }

    if (query[QUERY.LTE]) {
        const [field, value] = Object.entries(query[QUERY.LTE])[0];
        return `${field} <= "${value}"`;
    }

    if (query[QUERY.REGEX]) {
        const [field, value] = Object.entries(query[QUERY.REGEX])[0];
        return `${field} LIKE "${value}"`;
    }

    if (query[QUERY.BETWEEN]) {
        const [field, values] = Object.entries(query[QUERY.BETWEEN])[0];
        return `${field} BETWEEN ${values[0]} AND ${values[1]}`;
    }
    return '';
};

export const getQueryStringFromGridFilterModel = (filterModel: Record<string, any>, selectedTypes: string[]) => {
    let filterString = `schemaType IN (` + selectedTypes.join(', ') + `)`;
    const filterClause = Object.keys(filterModel)
        .map((filterName) => {
            return (
                filterName.replace('properties.', '') +
                ' ' +
                filterModel[filterName].type?.toUpperCase() +
                " '" +
                filterModel[filterName].filter +
                "'"
            );
        })
        .join(` AND `);
    if (filterClause?.length > 0) {
        filterString = filterString + ' AND ' + filterClause;
    }
    return filterString;
};
export const extractSchemaTypes = (criteria) => {
    if (!criteria) return [];
    // Define a regular expression to capture the `schemaType` array
    const regex = /"schemaType":\[(.*?)\]/;

    // Use the regex to search for the `schemaType` list
    const match = criteria.match(regex);

    if (match && match[1]) {
        // Split the captured string into an array of values
        const schemaTypeList = match[1].split(',').map((item) => item.trim().replace(/"/g, '')); // Remove surrounding quotes
        return schemaTypeList;
    }
    return [];
};

export const buildCascadedQueries = (attributes: Attribute[], fieldName: string, values: Record<string, any>) => {
    return attributes
        .filter((attr) => Boolean(attr.constraint?.cascadingAttrs?.[fieldName] && attr.constraint?.dataType))
        .map((attribute) => {
            const value = values[attribute.name];
            if (!value) return null;

            const relationPath = `relation.${attribute.constraint.cascadingAttrs[fieldName]}.id`;
            const queryValues = Array.isArray(value) ? value : [value];

            return queryValues.length > 0 || !Array.isArray(value) ? buildInQuery(relationPath, queryValues) : null;
        })
        .filter(Boolean);
};
