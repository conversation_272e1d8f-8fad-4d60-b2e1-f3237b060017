/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { formatDateTime } from '@glidesystems/styleguide';
import Schema from '../schema/model/schema.model';
import Attribute from '../schema/model/attributes.model';
import split from 'lodash/split';

export const DEFAULT_SELECTED_COLUMNS = [
    { field: 'state.name', headerName: 'Status' },
    { field: 'lifecycle.name', headerName: 'Lifecycle' },
    { field: 'owner.name', headerName: 'Owner' },
];
export const FIXED_COLUMNS = [
    ...DEFAULT_SELECTED_COLUMNS,
    { field: 'properties.pendingChanges', headerName: 'Pending Changes' },
];

export const filterAndSortAttributes = (attributes: Record<string, Attribute>, attributeOrder: string[] = []) => {
    let flattenOrder = [];
    attributeOrder.forEach((order) => {
        if (order.includes(':')) {
            flattenOrder = flattenOrder.concat(split(split(order, ':')[1], ','));
        } else {
            flattenOrder.push(order);
        }
    });
    const filteredAttributes = Object.values(attributes)
        .filter((attr) => attr.visible && attr.name !== 'name')
        .sort((a, b) => flattenOrder.indexOf(a.name) - flattenOrder.indexOf(b.name));

    return filteredAttributes;
};

export const createLayoutFromSchema = (schema: Schema) => {
    let exportingColumns = {
        Name: '$.properties.name',
    };
    DEFAULT_SELECTED_COLUMNS.forEach((column) => (exportingColumns[column.headerName] = column.field));
    filterAndSortAttributes(schema.attributes, schema.entityType.attributeOrder).forEach(
        (attribute) => (exportingColumns[attribute.displayName] = `$.properties.${attribute.name}`)
    );
    return exportingColumns;
};
