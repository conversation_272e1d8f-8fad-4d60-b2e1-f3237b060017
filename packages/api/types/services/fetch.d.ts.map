{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../src/services/fetch.ts"], "names": [], "mappings": "AAiBA,OAAc,EAAE,YAAY,EAAE,KAAK,sBAAsB,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAExF,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAa,MAAM,sBAAsB,CAAC;AAEjF,oBAAY,MAAM;IACd,GAAG,QAAQ;IACX,IAAI,SAAS;IACb,GAAG,QAAQ;IACX,MAAM,WAAW;IACjB,OAAO,YAAY;CACtB;AAED,MAAM,WAAW,SAAS;IACtB,MAAM,EAAE,MAAM,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAEjD,eAAO,MAAM,SAAS,SAAU,OAAO,WAAW,MAAM,SAEvD,CAAC;AAEF,eAAO,MAAM,YAAY,SAAU,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAG,MAOxE,CAAC;AAEF,UAAU,UAAU;IAChB,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3B,cAAc,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;IACtC,YAAY,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;IACpC,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,OAAO,CAAC,EAAE,sBAAsB,CAAC;IACjC,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,iBAAiB,CAAC,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC/C,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,KAAK,IAAI,CAAC;CACnD;AAMD,eAAO,MAAM,WAAW,aAAoB,CAAC;AAC7C,eAAO,MAAM,KAAK,+LAiBf,UAAU,KAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CA2EzC,CAAC;AAEF,eAAe,KAAK,CAAC"}