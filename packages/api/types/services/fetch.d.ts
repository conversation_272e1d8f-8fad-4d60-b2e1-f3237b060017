import { ResponseType, type RawAxiosRequestHeaders, AxiosResponse } from 'axios';
import { FetchQueryOptions, QueryClient } from '@tanstack/query-core';
export declare enum Method {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE",
    OPTIONS = "OPTIONS"
}
export interface UrlFormat {
    method: Method;
    url: string;
}
export type UrlGroup = Record<string, UrlFormat>;
export declare const invariant: (cond: boolean, message: string) => void;
export declare const generatePath: (path: string, params: Record<string, any>) => string;
interface FetchProps {
    url: string;
    params?: Record<string, any>;
    method: Method;
    qs?: Record<string, any>;
    data?: Record<string, any>;
    successMessage?: string | JSX.Element;
    errorMessage?: string | JSX.Element;
    shouldShow404?: boolean;
    shouldShow403?: boolean;
    skipToast?: boolean;
    skipDetails?: boolean;
    headers?: RawAxiosRequestHeaders;
    signal?: AbortSignal;
    responseType?: ResponseType;
    fetchQueryOptions?: Partial<FetchQueryOptions>;
    onUploadProgress?: (progressEvent: any) => void;
}
export declare const queryClient: QueryClient;
export declare const fetch: ({ url, params, method, qs, data, successMessage, errorMessage, shouldShow404, shouldShow403, skipToast, skipDetails, headers, signal, responseType, fetchQueryOptions, onUploadProgress, }: FetchProps) => Promise<AxiosResponse<any>>;
export default fetch;
//# sourceMappingURL=fetch.d.ts.map