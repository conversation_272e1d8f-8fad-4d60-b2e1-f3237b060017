import Attribute from '../../schema/model/attributes.model';
export default interface Classification {
    id: string;
    name: string;
    description: string;
    isSystem: boolean;
    updatedAt: string;
    createdAt: string;
    isDisabled: boolean;
    permissions: {
        canRead?: boolean;
        canEdit?: boolean;
    };
    attributes: Attribute[];
}
//# sourceMappingURL=classfication.model.d.ts.map