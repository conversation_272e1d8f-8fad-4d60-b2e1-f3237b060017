import { Method } from '../../services/fetch';
declare const classificationUrls: {
    getClassificationTree: {
        method: Method;
        url: string;
    };
    createClassification: {
        method: Method;
        url: string;
    };
    getClassificationDetail: {
        method: Method;
        url: string;
    };
    updateClassification: {
        method: Method;
        url: string;
    };
    deleteClassification: {
        method: Method;
        url: string;
    };
    createClassificationAttribute: {
        method: Method;
        url: string;
    };
    deleteAttribute: {
        method: Method;
        url: string;
    };
};
export default classificationUrls;
//# sourceMappingURL=index.d.ts.map