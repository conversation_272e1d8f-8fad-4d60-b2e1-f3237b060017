import type SysRoot from '../../entity/model/entity.model';
import { type RELATION_DIRECTION } from '../../constants/system-type';
import Attribute from './attributes.model';
type RelationSideType = 'FLOAT' | 'REPLICATED' | 'NONE';
type RelationFloatType = 'IMMEDIATE' | 'STATE_BASE';
export interface RelationSide {
    type: RelationSideType;
    floatType: RelationFloatType;
    floatStateCondition: string;
}
export interface RelationTypePermission {
    onCreate: string[];
    onDelete: string[];
}
export interface RelationType extends SysRoot {
    fromEntityType: string;
    toEntityType: string;
    required: boolean;
    visible: boolean;
    singleRelation: boolean;
    onFromSideRevise: RelationSide;
    onFromSideClone: RelationSide;
    onToSideRevise: RelationSide;
    onToSideClone: RelationSide;
    fromEntityPermissions: RelationTypePermission;
    toEntityPermission: RelationTypePermission;
    direction: RELATION_DIRECTION.OUTGOING | RELATION_DIRECTION.INCOMING;
    displayName: string;
    constraint?: {
        searchCriteria?: string;
    };
}
export interface RelationSchema {
    relationType: RelationType;
    attributes?: Record<string, Attribute>;
}
export {};
//# sourceMappingURL=relation.model.d.ts.map