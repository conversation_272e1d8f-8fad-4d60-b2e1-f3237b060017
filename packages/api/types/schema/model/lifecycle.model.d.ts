import Lifecycle from '../../lifecycles/model/lifecycle.model';
import LifecycleState from './lifecycle-state.model';
interface NextLifeCycleSchema {
    id: string;
    name: string;
    description: string;
}
export default interface Lifecycles {
    default: boolean;
    lifeCycle: Lifecycle;
    states: LifecycleState[];
    nextLifeCycles: NextLifeCycleSchema[];
}
export {};
//# sourceMappingURL=lifecycle.model.d.ts.map