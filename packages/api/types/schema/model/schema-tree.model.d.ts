import Classification from '../../classification/model/classfication.model';
import Attribute from './attributes.model';
import { RelationType } from './relation.model';
export default interface SchemaTree {
    id: string;
    name: string;
    description: string;
    extendable: boolean;
    abstract: boolean;
    subTypes?: SchemaTree[];
    displayName?: string;
    uniqueKeys: string[];
    relations?: RelationType[];
    classifications: Classification[];
    disabled: boolean;
    masterModel: boolean;
    system: boolean;
    visible: boolean;
    attributes?: Attribute[];
}
//# sourceMappingURL=schema-tree.model.d.ts.map