import Schema from "./model/schema.model";
import SchemaTree from "./model/schema-tree.model";
import EntityType from "./model/entity-type.model";
import AttributePageInfo from "./model/attributes-info.model";
import EntityLifeCycle from "../schema/model/entity-lifecycle.model";
declare const schemaService: {
    getSchemaTree(): Promise<import("axios").AxiosResponse<SchemaTree, any>>;
    getSchema(entityTypeName: string): Promise<import("axios").AxiosResponse<Schema, any>>;
    updateEntityType(entityTypeName: string, entityType: EntityType): Promise<import("axios").AxiosResponse<EntityType, any>>;
    createEntityAttribute(entityTypeName: string, attributes: any): Promise<import("axios").AxiosResponse<EntityType, any>>;
    updateEntityAttribute(entityTypeName: string, attributes: any): Promise<import("axios").AxiosResponse<EntityType, any>>;
    deleteEntityAttribute(entityTypeName: string, attributeName: string): Promise<import("axios").AxiosResponse<EntityType, any>>;
    createEntitySubType(entityTypeName: string, subType: any): Promise<import("axios").AxiosResponse<EntityType, any>>;
    getAttributes(): Promise<import("axios").AxiosResponse<AttributePageInfo, any>>;
    deleteEntityType(entityTypeName: string): Promise<import("axios").AxiosResponse<any, any>>;
    fetchEntityLifecycle(entityTypeName: string): Promise<import("axios").AxiosResponse<EntityLifeCycle, any>>;
    updateDefaultLifecycle(entityTypeName: string, lifecycleId: string): Promise<import("axios").AxiosResponse<any, any>>;
    getAllLifeCycles(searchTerm?: string, offset?: number, limit?: number): Promise<import("axios").AxiosResponse<EntityType, any>>;
    getLifeCycle(lifecycleId: string): Promise<import("axios").AxiosResponse<EntityType, any>>;
    assignLifeCycles(data: any, entityTypeName: any): Promise<import("axios").AxiosResponse<EntityType, any>>;
    unassignLifeCycles(data: any, entityTypeName: any): Promise<import("axios").AxiosResponse<EntityType, any>>;
    deleteLifecycle(id: string): Promise<import("axios").AxiosResponse<any, any>>;
    createLifecycle(lifecycle: any): Promise<import("axios").AxiosResponse<any, any>>;
    createRelation(relation: any): Promise<import("axios").AxiosResponse<any, any>>;
    updateLifecycle({ id, ...data }: {
        [x: string]: any;
        id: any;
    }): Promise<import("axios").AxiosResponse<any, any>>;
    deleteRelation(fromEntityTypeName: any, relationName: any, toEntityTypeName: any): Promise<import("axios").AxiosResponse<any, any>>;
};
export default schemaService;
//# sourceMappingURL=schema.service.d.ts.map