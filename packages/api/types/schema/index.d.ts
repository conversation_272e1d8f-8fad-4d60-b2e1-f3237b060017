import { Method } from '../services/fetch';
declare const schemaUrls: {
    readonly getSchemaTree: {
        readonly method: Method.GET;
        readonly url: `${string}/schema`;
    };
    readonly getSchemaDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly updateEntityType: {
        readonly method: Method.PUT;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly createEntityAttribute: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/attribute`;
    };
    readonly updateAttribute: {
        readonly method: Method.PUT;
        readonly url: `${string}/attribute/:attributeName`;
    };
    readonly deleteEntityAttribute: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName/attribute/:attributeName`;
    };
    readonly createEntitySubType: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/schema`;
    };
    readonly getListAttribute: {
        readonly method: Method.GET;
        readonly url: `${string}/attribute`;
    };
    readonly deleteEntityType: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName`;
    };
    readonly fetchEntityLifecycle: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly updateDefaultLifecycle: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/life-cycle/:lifecycleId/set-default`;
    };
    readonly getListLifeCycle: {
        readonly method: Method.GET;
        readonly url: `${string}/life-cycle`;
    };
    readonly getLifeCycle: {
        readonly method: Method.GET;
        readonly url: `${string}/life-cycle/:lifecycleId`;
    };
    readonly assignLifeCycles: {
        readonly method: Method.POST;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly unassignLifeCycles: {
        readonly method: Method.DELETE;
        readonly url: `${string}/schema/:entityTypeName/life-cycle`;
    };
    readonly deleteLifecycle: {
        readonly method: Method.DELETE;
        readonly url: `${string}/life-cycle/:id`;
    };
    readonly createLifecycle: {
        readonly method: Method.POST;
        readonly url: `${string}/life-cycle`;
    };
    readonly createRelation: {
        readonly method: Method.POST;
        readonly url: `${string}/relation`;
    };
    readonly updateLifecycle: {
        readonly method: Method.PUT;
        readonly url: `${string}/life-cycle/:lifeCycleId`;
    };
    readonly deleteRelation: {
        readonly method: Method.DELETE;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`;
    };
    readonly getRelation: {
        readonly method: Method.GET;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`;
    };
    readonly updateRelation: {
        readonly method: Method.PUT;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName`;
    };
    readonly createRelationAttribute: {
        readonly method: Method.POST;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute`;
    };
    readonly unassignRelationAttribute: {
        readonly method: Method.DELETE;
        readonly url: `${string}/relation/:fromEntityTypeName/:relationName/:toEntityTypeName/attribute/:attributeName`;
    };
    readonly getQuantityGroups: {
        readonly method: Method.GET;
        readonly url: `${string}/unit/quantity-kind`;
    };
    readonly getQuantityUnits: {
        readonly method: Method.GET;
        readonly url: `${string}/unit/:groupId/quantity-type`;
    };
    readonly getPermissionRoles: {
        readonly method: Method.GET;
        readonly url: `${string}/permission-role`;
    };
    readonly getDigitalThreadsByEntityType: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/digital-thread`;
    };
    readonly getSchemaClassification: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/classification`;
    };
    readonly getSchemaProcess: {
        readonly method: Method.GET;
        readonly url: `${string}/schema/:entityType/process`;
    };
};
export default schemaUrls;
//# sourceMappingURL=index.d.ts.map