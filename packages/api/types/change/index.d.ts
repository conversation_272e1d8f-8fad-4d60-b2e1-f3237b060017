import { Method } from '../services/fetch';
declare const changeUrls: {
    readonly getIssueSummary: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/issue-summary`;
    };
    readonly getRevisionIssueSummary: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityMasterId/revision-summary`;
    };
    readonly updateAttributePlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/update-attribute`;
    };
    readonly addComponentPlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/add-component`;
    };
    readonly deleteComponentPlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/delete-component`;
    };
    readonly addContextualAlternatePlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/add-contextual-alternate`;
    };
    readonly addGlobalAlternatePlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/add-global-alternate`;
    };
    readonly removeContextualAlternatePlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/remove-contextual-alternate`;
    };
    readonly removeGlobalAlternatePlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/remove-global-alternate`;
    };
    readonly replaceComponentPlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/replace-component`;
    };
    readonly swapComponentPlan: {
        readonly method: Method.POST;
        readonly url: `${string}/planned-change/:entityId/swap-component`;
    };
    readonly getChangeRequestAffectedItems: {
        readonly method: Method.GET;
        readonly url: `${string}/change-request/:entityId/affected-item`;
    };
    readonly getChangeOrderAffectedItems: {
        readonly method: Method.GET;
        readonly url: `${string}/change-order/:entityId/affected-item`;
    };
    readonly getPlannedChangeBomSummary: {
        readonly method: Method.GET;
        readonly url: `${string}/planned-change/:entityId/bom-change-summary`;
    };
    readonly createChangeOrder: {
        readonly method: Method.POST;
        readonly url: `${string}/change-order`;
    };
};
export default changeUrls;
//# sourceMappingURL=index.d.ts.map