import { Method } from '../services/fetch';
import { AxiosResponse } from 'axios';
interface RequestData {
    subParams: Record<string, any>;
    body: any;
    url: string;
    method: Method;
}
interface RequestDataWithSubRequests extends RequestData {
    subRequests?: Partial<RequestData>[];
}
export type BatchRequestData = Partial<RequestDataWithSubRequests>;
export interface BatchRequestResponse<T = any> {
    success: boolean;
    data?: AxiosResponse<T, any>[];
    error?: any;
}
interface BatchRequest {
    url: string;
    method: Method;
    data: Array<Record<string, any>>;
    maxChunkRequest?: number;
}
interface BatchRequestWithProgress extends BatchRequest {
    onProgressChange?: (progress: number) => void;
    signal: AbortSignal;
}
export interface HandleBatchRequest<T> {
    response: BatchRequestResponse<T>;
    statusCodeOnSuccess?: number[];
    onSuccess?: () => void;
    onFailed?: () => void;
    successMessage?: string;
    failedMessage?: string;
}
declare const createBatchReqData: (method: Method, url: string, data: BatchRequestData[]) => BatchRequestData[];
export declare const batchRequest: ({ url, method, data, maxChunkRequest, }: BatchRequest) => Promise<BatchRequestResponse>;
export declare const batchWithProgress: ({ url, method, data, maxChunkRequest, onProgressChange, signal, }: BatchRequestWithProgress) => Promise<BatchRequestResponse>;
export declare const handleBatchRequestRes: ({ response, statusCodeOnSuccess, onSuccess, onFailed, successMessage, failedMessage, }: HandleBatchRequest<any>) => void;
export default createBatchReqData;
//# sourceMappingURL=batchRequest.d.ts.map