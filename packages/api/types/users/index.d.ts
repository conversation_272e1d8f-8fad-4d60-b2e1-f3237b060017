import { Method } from '../services/fetch';
declare const userUrls: {
    getUsers: {
        method: Method;
        url: string;
    };
    createUser: {
        method: Method;
        url: string;
    };
    deleteUser: {
        method: Method;
        url: string;
    };
    getUser: {
        method: Method;
        url: string;
    };
    updateUser: {
        method: Method;
        url: string;
    };
    deactivateUser: {
        method: Method;
        url: string;
    };
    activateUser: {
        method: Method;
        url: string;
    };
    getRoles: {
        method: Method;
        url: string;
    };
};
export default userUrls;
//# sourceMappingURL=index.d.ts.map