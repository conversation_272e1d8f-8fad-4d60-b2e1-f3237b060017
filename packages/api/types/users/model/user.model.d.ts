export interface UserAttributes {
    companyId: string[];
    createdBy: string[];
    referenceId: string[];
}
export default interface User {
    firstName: string;
    lastName: string;
    id: string;
    email: string;
    enabled: boolean;
    createdTimestamp: string;
    username: string;
    emailVerified: boolean;
    attributes: UserAttributes;
    name: string;
    isAdmin: boolean;
    isSuperAdmin: boolean;
    roles: string[];
}
//# sourceMappingURL=user.model.d.ts.map