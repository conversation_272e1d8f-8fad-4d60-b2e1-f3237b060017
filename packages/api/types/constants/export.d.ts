export declare enum ExportType {
    EXCEL = "EXCEL",
    CSV = "CSV",
    PDX = "PDX"
}
export declare const EXPORT_TYPE_TO_EXTENSION: {
    readonly CSV: ".csv";
    readonly EXCEL: ".xls";
    readonly PDX: ".xml";
};
export declare enum ExportLayout {
    TABLE_LAYOUT = "TABLE_LAYOUT",
    SINGLE_ENTITY_LAYOUT = "SINGLE_ENTITY_LAYOUT"
}
export declare const EXPORT_EXTENSION_TO_TYPE: {
    ".csv": ExportType;
    ".xls": ExportType;
    ".xml": ExportType;
};
export declare const OTHERS_EXPORT_GROUP_LABEL = "Others";
//# sourceMappingURL=export.d.ts.map