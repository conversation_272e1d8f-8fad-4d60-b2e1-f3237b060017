.api-variables:
  variables:
    PACKAGE_DIR: packages/api

aws-stag-api-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .api-variables

aws-stag-api-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .api-variables
  needs:
    - aws-stag-api-package

gcp-stag-api-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .api-variables

gcp-stag-api-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .api-variables
  needs:
    - gcp-stag-api-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-api-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .api-variables

gcp-uat-api-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .api-variables
  needs:
    - gcp-uat-api-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json