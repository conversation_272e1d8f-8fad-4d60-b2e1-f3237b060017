import React from 'react';
interface DialogState {
    open: boolean;
    title?: string;
    content?: React.ReactNode;
    openDialog: (title: string, content: React.ReactNode) => void;
    closeDialog: () => void;
}
export declare const useOverflowingCellsDialogStore: import("zustand").UseBoundStore<import("zustand").StoreApi<DialogState>>;
export declare const OverflowingContentRenderer: () => import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=OverflowingContentRenderer.d.ts.map