import React from 'react';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
export declare const classes: {
    rteRoot: string;
    focus: string;
    readOnly: string;
};
export declare const Root: React.ComponentType;
declare const RichTextEditor: {
    ({ field, form: { touched, errors, setFieldValue, setFieldTouched }, meta, defaultRte, readOnly, ...props }: {
        [x: string]: any;
        field: any;
        form: {
            touched: any;
            errors: any;
            setFieldValue: any;
            setFieldTouched: any;
        };
        meta: any;
        defaultRte: any;
        readOnly: any;
    }): import("react/jsx-runtime").JSX.Element;
    defaultProps: {
        defaultRte: boolean;
    };
};
export default RichTextEditor;
//# sourceMappingURL=index.d.ts.map