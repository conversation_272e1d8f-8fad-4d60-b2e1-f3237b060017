/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    buildContainsQuery,
    buildOrOperatorQuery,
    SYSTEM_ENTITY_TYPE,
    fetch as apiFetch,
    workspaceUrls,
    getAvatarUrl,
} from '@glidesystems/api';
import {
    Autocomplete,
    Avatar,
    Box,
    Button,
    Chip,
    CircularProgress,
    ClickAwayListener,
    InputAdornment,
    Popper,
    PopperProps,
    styled,
    Tab,
    TabProps,
    Tabs,
    TextField,
    ThemeProvider,
    Typography,
} from '@mui/material';
import { Dispatch, ReactNode, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { EntityDetail } from '../utils/checkingPermission';
import { useSchemaTree, useWorkspace } from '@glidesystems/caching-store';
import debounce from 'lodash/debounce';
import { SearchIcon } from '../components/icons/Icons';
import { MainTooltip } from '../glidesystems-styleguide';
import themes from '../themes';

const StyledPopper = styled(Popper)(({ theme }) => ({
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.12)',
    background: '#FFFFFF',
    '& .MuiPaper-root': {
        borderRadius: 0,
        backgroundColor: theme.palette.glide.background.normal.white,
        boxShadow: 'none',
    },
    '& .MuiAutocomplete-listbox': {
        backgroundColor: theme.palette.glide.background.normal.white,
    },
}));
const TabItem = styled(Tab)<TabProps>(({ theme }) => ({
    fontSize: '14px',
    minHeight: '19px',
    marginRight: '2px',
    padding: '12px 40px 8px 12px',
    textTransform: 'none',
    color: theme.palette.glide.text.normal.inversePrimary,
    '&.Mui-selected': {
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 700,
    },
    '&.Mui-disabled': {
        color: '#C2C2C2',
        borderColor: '#C2C2C2',
    },
}));
const StyledTabs = styled(Tabs)(({ theme }) => ({
    marginTop: '8px',
    minHeight: '28px',
    marginBottom: '-2px',
    '& .MuiTabs-indicator': { backgroundColor: theme.palette.glide.stroke.normal.main },
}));

const AGENT_TYPE_ORDER = {
    Person: 1,
    Team: 2,
    Department: 3,
    InternalCompany: 4,
    ExternalCompany: 5,
};
const PopperComponent = ({
    setOptions,
    ...props
}: PopperProps & {
    setOptions: Dispatch<SetStateAction<EntityDetail[]>>;
}) => {
    const [selectedTab, setSelectedTab] = useState<string>(SYSTEM_ENTITY_TYPE.PERSON);
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const abortRef = useRef<AbortController | null>(null);
    const agentTypes = useSchemaTree((state) => {
        return Object.values(state.schemaTreeMap || {})
            .filter((schema) => schema.path.includes(SYSTEM_ENTITY_TYPE.AGENT) && !schema.abstract)
            .sort((a, b) => {
                const rankA =
                    AGENT_TYPE_ORDER[a.name] !== undefined ? AGENT_TYPE_ORDER[a.name] : Number.MAX_SAFE_INTEGER;
                const rankB =
                    AGENT_TYPE_ORDER[b.name] !== undefined ? AGENT_TYPE_ORDER[b.name] : Number.MAX_SAFE_INTEGER;

                if (rankA !== rankB) return rankA - rankB;

                const keyA = a.displayName || a.name || '';
                const keyB = b.displayName || b.name || '';
                return keyA.localeCompare(keyB);
            });
    });

    const onTabChanged = (e, value) => {
        setSearchText('');
        setSelectedTab(value);
    };

    const fetchData = useCallback(
        debounce(async (entityType, searchText) => {
            try {
                abortRef.current?.abort();
                abortRef.current = new AbortController();
                setLoading(true);
                let query;
                if (searchText) {
                    const searchQuery = [
                        buildContainsQuery('name', searchText),
                        buildContainsQuery('description', searchText),
                        buildContainsQuery('title', searchText),
                    ];
                    if (entityType === SYSTEM_ENTITY_TYPE.PERSON) {
                        searchQuery.push(buildContainsQuery('email', searchText));
                    }
                    query = JSON.stringify(buildOrOperatorQuery(searchQuery));
                }
                const {
                    data: { agents },
                } = await apiFetch({
                    ...workspaceUrls.getAgents,
                    params: {
                        workspaceName: useWorkspace.getState().selectedWorkspace?.name || '',
                        agentType: entityType,
                    },
                    qs: {
                        query,
                    },
                    signal: abortRef.current.signal,
                });
                setOptions(agents);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        }, 300),
        []
    );

    useEffect(() => {
        fetchData(selectedTab, searchText);
    }, [selectedTab, searchText]);

    return (
        <StyledPopper {...props}>
            <Box sx={{ padding: '8px' }}>
                <TextField
                    autoFocus
                    size="small"
                    sx={{
                        '& input': {
                            paddingLeft: '4px !important',
                            fontSize: '14px',
                            height: '24px',
                        },
                    }}
                    fullWidth
                    placeholder="Type to search"
                    value={searchText}
                    onChange={(e) => {
                        setSearchText((e.target as any).value);
                    }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
                <StyledTabs
                    variant="scrollable"
                    value={selectedTab}
                    scrollButtons="auto"
                    onChange={onTabChanged}
                    allowScrollButtonsMobile
                >
                    {agentTypes.map((agentType) => (
                        <TabItem key={agentType.name} value={agentType.name} label={agentType.displayName} />
                    ))}
                </StyledTabs>
            </Box>
            {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '24px' }}>
                    <CircularProgress size={48} />
                </Box>
            ) : (
                <Box sx={{ maxHeight: '200px', overflow: 'auto' }}>{props.children as ReactNode}</Box>
            )}
        </StyledPopper>
    );
};
function stringToColor(string: string) {
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;

    const saturation = 65;
    const lightness = 72;

    return `hsl(${hue}deg ${saturation}% ${lightness}%)`;
}
const AssigneeAvatar = ({ name, email }: { name: string; email?: string }) => {
    return (
        <MainTooltip title={email}>
            <Avatar
                sizes="small"
                sx={{ bgcolor: stringToColor(name), width: 26, height: 26, border: '1px solid white' }}
                children={`${name.split(' ')[0][0]}${name.split(' ')?.[1]?.[0] || ''}`}
                className="avatar"
                alt={name}
                src={email ? getAvatarUrl(email) : ''}
            />
        </MainTooltip>
    );
};
const AgentOption = (props: React.HTMLAttributes<HTMLLIElement> & { key: any }, option: EntityDetail) => {
    const { name, email } = option.properties;
    return (
        <Box key={option.id} {...(props as any)}>
            <Typography
                sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    lineHeight: '16px',
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center',
                    padding: '4px 8px',
                }}
            >
                <AssigneeAvatar name={name} email={email} />
                {name}
            </Typography>
        </Box>
    );
};
const AgentDropdown = ({
    value,
    onChange,
    error,
    helperText,
    onBlur = () => {},
}: {
    value: EntityDetail;
    onChange: (value: EntityDetail) => void;
    error?: boolean;
    helperText?: string;
    onBlur?: () => void;
}) => {
    const [open, setOpen] = useState(false);
    const [options, setOptions] = useState<EntityDetail[]>([]);
    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);
    const popperComponent = useMemo(
        () => (props: PopperProps) => <PopperComponent {...props} setOptions={setOptions} />,
        []
    );
    return (
        <ThemeProvider theme={themes.default}>
            <ClickAwayListener
                onClickAway={() => {
                    setOpen(false);
                }}
            >
                <Box>
                    <Autocomplete
                        inputValue=""
                        options={options}
                        fullWidth
                        open={open}
                        onFocus={(e) => {
                            setOpen(true);
                        }}
                        onChange={(_, newValue) => onChange(newValue)}
                        value={value}
                        multiple={false}
                        getOptionLabel={(option) => {
                            return option?.properties?.name ?? '';
                        }}
                        onBlur={onBlur}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        disabled={!schemaTreeMap}
                        ListboxProps={{
                            sx: {
                                maxHeight: '200px',
                                '& .MuiAutocomplete-option, .MuiAutocomplete-option[aria-selected="true"]': {
                                    '&:hover': {
                                        background: '#334466 !important',
                                        color: '#fff !important',
                                    },
                                },
                            },
                        }}
                        renderOption={AgentOption}
                        disableCloseOnSelect
                        autoFocus={false}
                        PopperComponent={popperComponent}
                        renderInput={(params) => (
                            <TextField
                                autoComplete="off"
                                label="Assignee"
                                placeholder={!schemaTreeMap ? 'Loading...' : ''}
                                error={error}
                                helperText={helperText}
                                {...params}
                                InputLabelProps={{ shrink: true }}
                                fullWidth
                                size="small"
                                InputProps={{
                                    ...params.InputProps,
                                    startAdornment: (
                                        <>
                                            {value && (
                                                <MainTooltip
                                                    title={
                                                        value?.properties?.email ||
                                                        schemaTreeMap?.[value?.properties?.type]?.displayName ||
                                                        value?.properties?.type
                                                    }
                                                >
                                                    <Chip
                                                        size="small"
                                                        avatar={
                                                            <Avatar
                                                                alt={value?.properties?.name ?? ''}
                                                                src={
                                                                    value?.properties?.email
                                                                        ? getAvatarUrl(value?.properties?.email)
                                                                        : ''
                                                                }
                                                            />
                                                        }
                                                        label={value?.properties?.name ?? ''}
                                                    />
                                                </MainTooltip>
                                            )}
                                            {params.InputProps.startAdornment}
                                        </>
                                    ),
                                }}
                                inputProps={{
                                    ...params.inputProps,
                                    readOnly: true,
                                    tabIndex: -1,
                                }}
                                sx={{
                                    '& input': { caretColor: 'transparent' },
                                }}
                            />
                        )}
                    />
                </Box>
            </ClickAwayListener>
        </ThemeProvider>
    );
};

export default AgentDropdown;
