/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
    Box,
    Drawer,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    styled,
    Avatar,
    Collapse,
    useTheme,
    useMediaQuery,
} from '@mui/material';
import { authenticationService, IDENTITY_SERVICE_URL, getAvatarUrl } from '@glidesystems/api';
import classNames from 'classnames';
import { classes, Root } from './styles';
import type KeyCloak from 'keycloak-js';

import { MainTooltip, ExpandMoreIcon } from '@glidesystems/styleguide';
import { useSideBar, useAuth } from '@glidesystems/caching-store';
import { DEFAULT_ROUTES, RouteType } from './constants/routes';
import { Link, useLocation } from 'react-router-dom';
import { AccountIcon, AdminIcon, LogoutIcon } from './assets/icon/Icon';
import get from 'lodash/get';

function handleOpenAccountPage() {
    const tenant = authenticationService.getTenant();
    window.open(`${IDENTITY_SERVICE_URL}/auth/realms/${tenant}/account`, '_blank').focus();
}

function handleOpenAdminSite() {
    const tenant = authenticationService.getTenant();
    window.open([location.protocol, '//', location.host, '/admin'].join(''), '_blank').focus();
}

const StyledListItem = styled(ListItem)(({ theme }) => ({
    display: 'block',
    backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
    color: theme.palette.glide.text.normal.inverseSecondary,
    '& .sidebarButton': {
        padding: '12px 16px',
        '& .sidebarText': {
            marginTop: 0,
            marginBottom: 0,
            '& span': {
                fontWeight: 500,
                fontSize: '14px',
                lineHeight: '135%',
                letterSpacing: '-0.1px',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
            },
        },
        '& .sidebarItemIcon': {
            width: 40,
            minWidth: 40,
            color: theme.palette.glide.text.normal.inverseSecondary,
        },
        '&:hover': {
            backgroundColor: theme.palette.glide.background.hover.inverseSecondary,
            color: theme.palette.glide.text.normal.inverseSecondary,
            ' & .sidebarText span': {
                fontWeight: 700,
                letterSpacing: '0.2px',
            },
            '& .sidebarItemIcon svg': {
                fill: theme.palette.glide.text.normal.inverseSecondary,
            },
        },
        '&.active': {
            backgroundColor: theme.palette.glide.background.active.primary,
            color: theme.palette.glide.text.normal.tertiary,
            ' & .sidebarText span': {
                fontWeight: 700,
                letterSpacing: '0.2px',
            },
            '& .sidebarItemIcon svg': {
                fill: theme.palette.glide.text.normal.tertiary,
            },
        },
    },
}));

const SideBarLink = ({ link, icon, label, open, active = false }) => {
    return (
        //@ts-ignore
        <StyledListItem disablePadding component={Link} to={link}>
            <ListItemButton className={classNames('sidebarButton', { active: active })}>
                <ListItemIcon
                    className={classNames('sidebarItemIcon', {
                        open: open,
                    })}
                >
                    {icon}
                </ListItemIcon>
                <ListItemText
                    className="sidebarText"
                    primary={label}
                    sx={{ opacity: open ? 1 : 0, transition: 'ease-in-out 0.2s' }}
                />
            </ListItemButton>
        </StyledListItem>
    );
};

const Devider = styled(Box)(({ theme }) => ({
    width: '100%',
    height: '1px',
    backgroundColor: theme.palette.glide.stroke.normal.primary,
}));

const SideBar = ({
    routes = DEFAULT_ROUTES,
    authentication,
}: {
    routes: RouteType[];
    authentication: { keycloak: KeyCloak };
}) => {
    const location = useLocation();
    // global
    const { open, setOpen } = useSideBar();
    const { userInfo, setUserInfo } = useAuth();
    const [accountOpened, setAccountOpened] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const ACTIONS = useMemo(() => {
        return [
            {
                icon: <AccountIcon />,
                label: 'Account',
                onClick: handleOpenAccountPage,
                requiredAdmin: false,
            },
            {
                icon: <AdminIcon />,
                label: 'Admin Site',
                onClick: handleOpenAdminSite,
                requiredAdmin: true,
            },
            {
                icon: <LogoutIcon />,
                label: 'Log out',
                onClick: () => {
                    if (!authentication.keycloak.idToken) {
                        authentication.keycloak.idToken = window.sessionStorage.getItem('idToken');
                    }
                    authentication.keycloak.logout({ logoutMethod: 'POST' }).then(() => {
                        window.sessionStorage.clear();
                    });
                },
                requiredAdmin: false,
            },
        ];
    }, [authentication]);

    useEffect(() => {
        if (!userInfo) {
            authenticationService.getUserInfo().then((userInformation) => {
                setUserInfo(userInformation);
            });
        }
    }, [userInfo]);

    const email = get(userInfo, 'email');
    const userName = get(userInfo, 'name', 'User');
    const isAdmin = get(userInfo, 'isAdmin', false) || get(userInfo, 'isSuperAdmin', false);

    const onAccountClick = () => {
        if (!open) {
            setOpen(true);
        }
        setAccountOpened((prev) => !prev);
    };

    useEffect(() => {
        if (!open) {
            setAccountOpened(false);
        }
    }, [open]);

    const isNoWorkspace = location.pathname === '/no-workspace';

    return (
        <Root>
            <Drawer
                variant={isMobile ? 'temporary' : 'permanent'}
                sx={
                    isMobile
                        ? {
                              '& .MuiPaper-root': {
                                  width: '250px',
                                  backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
                                  maxWidth: '50%',
                              },
                          }
                        : {}
                }
                className={classNames(classes.drawer, {
                    [classes.drawerOpen]: open,
                    [classes.drawerClose]: !open,
                })}
                classes={{
                    paper: classNames(classes.drawer, {
                        [classes.drawerOpen]: open,
                        [classes.drawerClose]: !open,
                    }),
                }}
                open={open}
                onClose={() => setOpen(false)}
            >
                <Devider sx={{ marginTop: '51px' }} />
                <List sx={{ pt: 0 }}>
                    {routes.map((props) => {
                        const isActive = location.pathname.startsWith(props.link);
                        return (
                            <MainTooltip
                                key={`sidebar-link-${props.id}`}
                                placement="right"
                                title={open ? '' : props.label}
                                className={isNoWorkspace ? 'dim-when-disabled none-pe-when-disabled' : ''}
                            >
                                <span>
                                    <SideBarLink {...props} open={open} active={isActive} />
                                </span>
                            </MainTooltip>
                        );
                    })}
                </List>
                <List sx={{ marginTop: 'auto', display: 'flex', flexDirection: 'column', p: 0 }}>
                    <Devider />
                    <StyledListItem
                        disablePadding
                        sx={{
                            '& .sidebarButton': {
                                px: open ? '24px' : 0,
                                justifyContent: 'center',
                            },
                        }}
                    >
                        <MainTooltip title={userName} enterDelay={1000}>
                            <ListItemButton onClick={onAccountClick} className="sidebarButton">
                                <Avatar alt={userName} src={email ? getAvatarUrl(email) : ''} />
                                {open && (
                                    <>
                                        <ListItemText
                                            className="sidebarText"
                                            primary={userName}
                                            sx={{
                                                opacity: open ? 1 : 0,
                                                transition: 'ease-in-out 0.2s',
                                                ml: '16px',
                                                '& span': {
                                                    fontWeight: 500,
                                                    fontSize: '14px',
                                                    color: (theme) => theme.palette.glide.text.normal.inverseSecondary,
                                                },
                                            }}
                                        />
                                        <ExpandMoreIcon sx={{ transform: accountOpened ? 'rotate(180deg)' : 'none' }} />
                                    </>
                                )}
                            </ListItemButton>
                        </MainTooltip>
                        <Collapse in={accountOpened} timeout="auto" unmountOnExit>
                            <List sx={{ ml: '40px' }}>
                                {ACTIONS.filter((action) => !action.requiredAdmin || isAdmin).map(
                                    ({ label, onClick, icon }) => (
                                        <ListItemButton onClick={onClick} className="sidebarButton" key={label}>
                                            <ListItemIcon className="sidebarItemIcon">{icon}</ListItemIcon>
                                            <ListItemText className="sidebarText" primary={label} />
                                        </ListItemButton>
                                    )
                                )}
                            </List>
                        </Collapse>
                    </StyledListItem>
                </List>
            </Drawer>
        </Root>
    );
};

export default SideBar;
