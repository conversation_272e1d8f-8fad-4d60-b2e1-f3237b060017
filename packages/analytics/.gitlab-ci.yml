.analytics-variables:
  variables:
    PACKAGE_DIR: packages/analytics

aws-stag-analytics-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .analytics-variables

aws-stag-analytics-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .analytics-variables
  needs:
    - aws-stag-analytics-package

gcp-stag-analytics-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .analytics-variables

gcp-stag-analytics-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .analytics-variables
  needs:
    - gcp-stag-analytics-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-analytics-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .analytics-variables

gcp-uat-analytics-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .analytics-variables
  needs:
    - gcp-uat-analytics-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json