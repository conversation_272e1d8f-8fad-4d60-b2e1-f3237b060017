/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { DetailClassificationSchema, DetailEntity, DetailSchema } from '../model';
import get from 'lodash/get';
import md5 from 'md5';
import set from 'lodash/set';
import { ATTRIBUTE_GROUP, CLASSIFICATION_GROUP } from '../constants/common';

export const hasRevisionAttribute = (detailEntity: DetailEntity): boolean => {
    return Boolean(detailEntity.entitySchema.attributes['revision']);
};

export const hasAttributeInSchema = (attributeName: string, schema: DetailSchema): boolean => {
    const attributeSchema = schema.attributes[attributeName];
    return Boolean(attributeSchema) && attributeSchema.visible;
};

export const getTitle = (entity) => {
    const revision = entity.properties.revision;
    return revision ? `${entity.properties.name} - ${revision}` : entity.properties.name;
};

export const getColumnRevisionHeader = (entity) => {
    return `Value in ${getTitle(entity)}`;
};

export const getAttributeSchema = (attributeName: string, schema: DetailSchema) => {
    return get(schema, ['attributes', attributeName]);
};

export const getClassificationAttributeSchema = (
    classificationName: string,
    attributeName: string,
    schema: Record<string, DetailClassificationSchema>
) => {
    return get(schema, [classificationName, 'attributes', attributeName]);
};

export const getProperty = (attributeName: string, detailEntity: DetailEntity): any => {
    return get(detailEntity, ['properties', attributeName]);
};

export const doesEntityHaveClassification = (classificationName: string, detailEntity: DetailEntity) => {
    return detailEntity.classifications.some((classification) => classification.name === classificationName);
};

export const groupAttributes = (
    sourceEntity: DetailEntity,
    targetEntity: DetailEntity,
    sourceSchema: DetailSchema,
    targetSchema: DetailSchema,
    classificationSchema: Record<string, DetailClassificationSchema>
) => {
    let groupedAttributes = [];

    compareSchemaAttributes(sourceSchema, targetSchema, sourceEntity, targetEntity, groupedAttributes);

    let combinedClassificationsMap = {};

    [...sourceEntity.classifications, ...targetEntity.classifications].forEach((classification) => {
        combinedClassificationsMap[classification.name] = classification;
    });

    for (const [classificationName] of Object.entries(combinedClassificationsMap)) {
        if (doesEntityHaveClassification(classificationName, sourceEntity)) {
            if (doesEntityHaveClassification(classificationName, targetEntity)) {
                // Common classification
                for (const [attributeName] of Object.entries(classificationSchema[classificationName].attributes)) {
                    const sourceValue = sourceEntity.properties[attributeName];
                    const targetValue = targetEntity.properties[attributeName];

                    const classificationGroup =
                        sourceValue === targetValue
                            ? CLASSIFICATION_GROUP.COMMON
                            : CLASSIFICATION_GROUP.COMMON_WITH_DIFFERENCES;
                    groupedAttributes.push({
                        ...getClassificationAttributeSchema(classificationName, attributeName, classificationSchema),
                        sourceValue,
                        targetValue,
                        classificationGroup,
                        classification: classificationName,
                    });
                }
            } else {
                // Unique on Source entity
                for (const [attributeName] of Object.entries(classificationSchema[classificationName].attributes)) {
                    const sourceValue = sourceEntity.properties[attributeName];
                    const targetValue = targetEntity.properties[attributeName];
                    groupedAttributes.push({
                        ...getClassificationAttributeSchema(classificationName, attributeName, classificationSchema),
                        sourceValue,
                        targetValue,
                        classificationGroup: `${CLASSIFICATION_GROUP.UNIQUE_ON} ${sourceEntity.properties.name}`,
                        classification: classificationName,
                    });
                }
            }
        } else {
            // Unique on Target entity
            for (const [attributeName] of Object.entries(classificationSchema[classificationName].attributes)) {
                const sourceValue = sourceEntity.properties[attributeName];
                const targetValue = targetEntity.properties[attributeName];
                groupedAttributes.push({
                    ...getClassificationAttributeSchema(classificationName, attributeName, classificationSchema),
                    sourceValue,
                    targetValue,
                    classificationGroup: `${CLASSIFICATION_GROUP.UNIQUE_ON} ${targetEntity.properties.name}`,
                    classification: classificationName,
                });
            }
        }
    }

    return groupedAttributes;
};

export const getUniqGroupName = (entity, groupPrefix = ATTRIBUTE_GROUP.UNIQUE_ON) => {
    const entityName = get(entity, ['properties', 'name']);
    return `${groupPrefix} ${entityName}`;
};

const compareSchemaAttributes = (
    sourceSchema: DetailSchema,
    targetSchema: DetailSchema,
    sourceEntity: DetailEntity,
    targetEntity: DetailEntity,
    groupedAttributes: any[]
) => {
    const combinedAttributesMap = {
        ...sourceSchema.attributes,
        ...targetSchema.attributes,
    };
    for (const [attributeName] of Object.entries(combinedAttributesMap)) {
        const sourceValue = sourceEntity.properties[attributeName];
        const targetValue = targetEntity.properties[attributeName];

        if (hasAttributeInSchema(attributeName, sourceSchema)) {
            // Common attributes
            if (hasAttributeInSchema(attributeName, targetSchema)) {
                const groupLabel =
                    sourceValue === targetValue ? ATTRIBUTE_GROUP.COMMON : ATTRIBUTE_GROUP.COMMON_WITH_DIFFERENCES;
                groupedAttributes.push({
                    ...getAttributeSchema(attributeName, sourceSchema),
                    sourceValue,
                    targetValue,
                    groupLabel,
                });
            } else {
                // Unique attributes in source entity
                groupedAttributes.push({
                    ...getAttributeSchema(attributeName, sourceSchema),
                    sourceValue,
                    targetValue,
                    groupLabel: getUniqGroupName(sourceEntity),
                });
            }
        } else if (hasAttributeInSchema(attributeName, targetSchema)) {
            groupedAttributes.push({
                ...getAttributeSchema(attributeName, targetSchema),
                sourceValue,
                targetValue,
                groupLabel: getUniqGroupName(targetEntity),
            });
        }
    }
};

export const getRowClass = (params, sourceEntity, targetEntity) => {
    const isGroup = get(params, ['node', 'group'], false);

    let key = isGroup ? get(params, ['node', 'key']) : get(params, ['data', 'groupLabel']);
    if (key === undefined) {
        key = get(params, ['data', 'classificationGroup']);
    }
    switch (key) {
        case ATTRIBUTE_GROUP.COMMON:
        case CLASSIFICATION_GROUP.COMMON:
            return 'common-row';
        case ATTRIBUTE_GROUP.COMMON_WITH_DIFFERENCES:
        case CLASSIFICATION_GROUP.COMMON_WITH_DIFFERENCES:
            return 'common-row with-diff';
        case getUniqGroupName(sourceEntity):
        case getUniqGroupName(sourceEntity, CLASSIFICATION_GROUP.UNIQUE_ON):
            return 'unique-row source';
        case getUniqGroupName(targetEntity):
        case getUniqGroupName(targetEntity, CLASSIFICATION_GROUP.UNIQUE_ON):
            return 'unique-row target';
    }
};

export const getEntityName = (entity) => {
    return get(entity, ['properties', 'name']);
};

export const getSummaryAttributesBar = (rowData: any[], sourceEntity: DetailEntity, targetEntity: DetailEntity) => {
    let common = 0,
        commonWithDiff = 0,
        uniqueOnSource = 0,
        uniqueOnTarget = 0,
        commonClassification = 0,
        commonClassificationWithDiff = 0,
        uniqueClassificationOnSource = 0,
        uniqueClassificationOnTarget = 0;
    rowData.forEach((row) => {
        // Attributes in Schema
        if (row.groupLabel === ATTRIBUTE_GROUP.COMMON) {
            common += 1;
        }
        if (row.groupLabel === ATTRIBUTE_GROUP.COMMON_WITH_DIFFERENCES) {
            commonWithDiff += 1;
        }
        if (row.groupLabel === getUniqGroupName(sourceEntity)) {
            // Unique on source entity
            uniqueOnSource += 1;
        }
        if (row.groupLabel === getUniqGroupName(targetEntity)) {
            uniqueOnTarget += 1;
        }
        if (row.classificationGroup === CLASSIFICATION_GROUP.COMMON) {
            commonClassification += 1;
        }
        if (row.classificationGroup === CLASSIFICATION_GROUP.COMMON_WITH_DIFFERENCES) {
            commonClassificationWithDiff += 1;
        }
        if (row.classificationGroup === getUniqGroupName(sourceEntity, CLASSIFICATION_GROUP.UNIQUE_ON)) {
            uniqueClassificationOnSource += 1;
        }
        if (row.classificationGroup === getUniqGroupName(targetEntity, CLASSIFICATION_GROUP.UNIQUE_ON)) {
            uniqueClassificationOnTarget += 1;
        }
    });
    return {
        attributes: [
            {
                id: 'common-attributes',
                source: {
                    name: 'Similar',
                    total: common,
                    color: '#13C2C2',
                },
                target: {
                    name: 'Different',
                    total: commonWithDiff,
                    color: '#FA8C16',
                },
                label: 'Common Attributes',
            },
            {
                id: 'unique-attributes',
                source: {
                    name: getEntityName(sourceEntity),
                    total: uniqueOnSource,
                    color: '#722ED1',
                },
                target: {
                    name: getEntityName(targetEntity),
                    total: uniqueOnTarget,
                    color: '#E4C916',
                },
                label: 'Unique Attributes',
            },
        ],
        classifications: [
            {
                id: 'commonClassification-classifications',
                source: {
                    name: 'Similar',
                    total: commonClassification,
                    color: '#13C2C2',
                },
                target: {
                    name: 'Different',
                    total: commonClassificationWithDiff,
                    color: '#FA8C16',
                },
                label: 'Common Classifications',
            },
            {
                id: 'uniqueClassification-attributes',
                source: {
                    name: getEntityName(sourceEntity),
                    total: uniqueClassificationOnSource,
                    color: '#722ED1',
                },
                target: {
                    name: getEntityName(targetEntity),
                    total: uniqueClassificationOnTarget,
                    color: '#E4C916',
                },
                label: 'Unique Classifications',
            },
        ],
    };
};

export const aggregateBomTree = (parentData, results = {}, groupedData) => {
    const {
        rowId: parentRowId,
        path: parentPath,
        component: { id: parentComponentId },
        componentPath,
    } = parentData;
    const bomsByAssemblyId = groupedData[parentComponentId];

    if (bomsByAssemblyId) {
        bomsByAssemblyId.forEach((bom) => {
            const rowId = md5(parentRowId + bom.id);
            const recursiveRowId = componentPath[bom.component.id];
            if (recursiveRowId || results[rowId]) {
                const newRowId = `recursive-${rowId}`;
                const duplicatedComponent = {
                    ...bom,
                    rowId: newRowId,
                    path: [...parentPath, newRowId],
                    level: parentPath.length,
                    componentPath: { ...componentPath, [newRowId]: newRowId },
                    uiStates: {
                        isRoot: false,
                        currentPermissions: bom.component.permissions,
                        parentPermissions: parentData.component.permissions,
                        parentComponentName: parentData.component.name,
                        isRecursive: true,
                    },
                };
                results[newRowId] = duplicatedComponent;
                set(results, [recursiveRowId, 'uiStates', 'isRecursiveParent'], true);

                return;
            }
            const currentBom = {
                ...bom,
                rowId,
                path: [...parentPath, rowId],
                level: parentPath.length,
                componentPath: { ...componentPath, [bom.component.id]: rowId },
                uiStates: {
                    isRoot: false,
                    currentPermissions: bom.component.permissions,
                    parentPermissions: parentData.component.permissions,
                    parentComponentName: parentData.component.name,
                    isRecursive: false,
                },
            };
            results[rowId] = currentBom;
            aggregateBomTree(currentBom, results, groupedData);
        });
    }
};

export const getBomAttributesFromSchema = (bomSchema: DetailSchema) => {
    return Object.values(bomSchema.attributes).filter(
        (attribute) => attribute.visible && !['name', 'title', 'description'].includes(attribute.name)
    );
};
