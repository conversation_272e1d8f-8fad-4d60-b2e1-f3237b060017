/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import { Thumbnail } from '@glidesystems/styleguide';
import { getThumbnailUrl } from '@glidesystems/api';

const NameRenderer = ({ value, data }) => {
    const {
        id,
        properties: { name, type, hasThumbnail },
    } = data;
    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Thumbnail
                hasThumbnail={hasThumbnail}
                url={getThumbnailUrl(type, id)}
                alt={name}
                size={{ width: 24, height: 24 }}
            />
            <Typography
                sx={{
                    textDecoration: 'none',
                    color: (theme) => theme.palette.info.main,
                    fontSize: '14px',
                    fontWeight: 400,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                }}
                component={Link}
                to={`/detail/${type}/${id}/properties`}
            >
                {value}
            </Typography>
        </Box>
    );
};

export default NameRenderer;
