/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { AnalyticsStore, Bom, ComparisonResultType, DetailEntitiesRequest, DetailEntity, DetailSchema } from '../model';
import {
    fetch,
    entityUrls,
    classificationUrls,
    schemaUrls,
    SYSTEM_ENTITY_TYPE,
    DEFAULT_CLIENT_SIDE_LIMIT,
    Attribute,
} from '@glidesystems/api';
import groupBy from 'lodash/groupBy';
import uniqBy from 'lodash/uniqBy';
import { BOM_VIEW_OPTIONS } from '../constants/common';
import { aggregateBomTree, getBomAttributesFromSchema } from '../util/helper';

const DEFAULT_STATES = {
    error: null,
    isLoading: true,
    sourceEntity: null,
    targetEntity: null,
    sourceSchema: null,
    targetSchema: null,
    sourceBom: null,
    targetBom: null,
    sourceBomSchema: null,
    targetBomSchema: null,
    showComparedItems: true,
};

export const useAppStore = create<AnalyticsStore>()((set, get) => ({
    ...DEFAULT_STATES,
    souceBomSchema: null,
    targetBomSchema: null,
    classificationSchema: {},
    getDetailEntities: async (request: DetailEntitiesRequest) => {
        const { sourceEntity, targetEntity, classifications, sourceSchema, targetSchema } = await getComparedEntities(
            request
        );
        let cachedClassifications = { ...get().classificationSchema };

        let newClassifications = classifications.filter(
            (classification) => !Boolean(cachedClassifications[classification.name])
        );

        const newClassificationSchemas = await getClassificationSchema(newClassifications);
        newClassificationSchemas.forEach((classificationSchema) => {
            cachedClassifications[classificationSchema.classification.name] = classificationSchema;
        });

        set({
            sourceEntity,
            targetEntity,
            sourceSchema,
            targetSchema,
            classificationSchema: cachedClassifications,
            isLoading: false,
        });
    },
    toggleShowComparedItems: () => {
        set({ showComparedItems: !get().showComparedItems });
    },
    getBomSchema: async (bomType: string, type: 'source' | 'target') => {
        const cachedSchema = type === 'source' ? get().sourceBomSchema : get().targetBomSchema;
        if (cachedSchema) return cachedSchema;

        const bomSchema = await getBomSchema(bomType);
        if (type === 'source') {
            set({ sourceBomSchema: bomSchema });
        } else {
            set({ targetBomSchema: bomSchema });
        }
        return bomSchema;
    },
    getBomComparisonData: async () => {
        const sourceEntity = get().sourceEntity;
        const targetEntity = get().targetEntity;
        const sourceBomName = sourceEntity.entitySchema.decompositionModel.name;
        const targetBomName = targetEntity.entitySchema.decompositionModel.name;

        const [sourceBom, targetBom, sourceBomSchema, targetBomSchema] = await Promise.all([
            fetchBom(sourceEntity.id),
            fetchBom(targetEntity.id),
            get().getBomSchema(sourceBomName, 'source'),
            get().getBomSchema(targetBomName, 'target'),
        ]);

        const [sourceResults, targetResults] = compareBom(
            sourceEntity,
            sourceBom,
            targetEntity,
            targetBom,
            sourceBomSchema,
            targetBomSchema
        );
        set({ sourceBom: sourceResults, targetBom: targetResults });
    },
    clearStore: () => {
        set(DEFAULT_STATES);
    },
}));

const buildTreePath = (rootEntity, bom): Record<string, Bom> => {
    const grouped = groupBy(bom, 'assemblyId');
    const rootId = rootEntity.id;
    const root = {
        id: rootId,
        rowId: rootId,
        path: [rootId],
        componentPath: {
            [rootId]: rootId,
        },
        component: {
            id: rootId,
            permissions: rootEntity.permissions,
            ...rootEntity.properties,
            alternates: rootEntity.alternates || [],
        },
        uiStates: {
            isRoot: true,
        },
    };
    let aggregatedTree = { [rootId]: root };
    aggregateBomTree(root, aggregatedTree, grouped);
    return aggregatedTree;
};

const compareBom = (
    sourceEntity: DetailEntity,
    sourceBomData: Bom[],
    targetEntity: DetailEntity,
    targetBomData: Bom[],
    sourceBomSchema: DetailSchema,
    targetBomSchema: DetailSchema
) => {
    let sourceTreeMap = buildTreePath(sourceEntity, sourceBomData);
    let targetTreeMap = buildTreePath(targetEntity, targetBomData);
    let sourceBomList = Object.values(sourceTreeMap);
    let targetBomList = Object.values(targetTreeMap);

    let firstLevelSourceBom = sourceBomList.filter((bom: Bom) => bom.level === 1);
    let firstLevelTargetBom = targetBomList.filter((bom: Bom) => bom.level === 1);
    let firstLevelGroup = {};

    const attributes = uniqBy(
        [...getBomAttributesFromSchema(sourceBomSchema), ...getBomAttributesFromSchema(targetBomSchema)],
        'name'
    );
    for (const sourceBom of firstLevelSourceBom) {
        if (sourceBom.compared) continue;
        let isSimilar = false;
        sourceBom.compared = true;
        for (const targetBom of firstLevelTargetBom) {
            if (targetBom.compared) continue;
            // Enhance to compare dynamic attributes
            // Similar component
            if (sourceBom.component.id === targetBom.component.id) {
                isSimilar = true;
                targetBom.compared = true;

                // Similar properties
                const sourceProperties = sourceBom.properties;
                const targetProperties = targetBom.properties;
                if (allAttributeEqual(attributes, sourceProperties, targetProperties)) {
                    firstLevelGroup = buildCommonRow(sourceBom, targetBom, firstLevelGroup);
                } else {
                    // Common with differences in properties
                    firstLevelGroup = buildCommonWithDiffRow(
                        sourceBom,
                        targetBom,
                        attributes,
                        sourceProperties,
                        targetProperties,
                        firstLevelGroup
                    );
                }
                break;
            }
        }
        if (!isSimilar) {
            sourceBom.comparisonResult = {
                result: ComparisonResultType.UNIQUE_ON_SOURCE,
            };
            firstLevelGroup = {
                ...firstLevelGroup,
                [sourceBom.rowId]: ComparisonResultType.UNIQUE_ON_SOURCE,
            };
        }
    }
    for (const targetBom of firstLevelTargetBom) {
        if (!targetBom.compared) {
            targetBom.compared = true;
            targetBom.comparisonResult = {
                result: ComparisonResultType.UNIQUE_ON_TARGET,
            };
            firstLevelGroup = {
                ...firstLevelGroup,
                [targetBom.rowId]: ComparisonResultType.UNIQUE_ON_TARGET,
            };
        }
    }
    let sourceResults = [],
        targetResults = [];
    sourceResults = combineComparisonResults(sourceBomList, sourceResults, firstLevelGroup, firstLevelSourceBom);
    targetResults = combineComparisonResults(targetBomList, targetResults, firstLevelGroup, firstLevelTargetBom);

    return [sourceResults, targetResults];
};

const getComparedEntities = async (request: DetailEntitiesRequest) => {
    const { sourceId, targetId, sourceType, targetType } = request;
    const [sourceEntity, targetEntity, sourceSchema, targetSchema] = await Promise.all([
        getDetailEntityById(sourceType, sourceId),
        getDetailEntityById(targetType, targetId),
        getDetailSchema(sourceType),
        getDetailSchema(targetType),
    ]);

    const classifications = uniqBy(
        sourceEntity.classifications.map((c) => c).concat(targetEntity.classifications.map((c) => c)),
        'name'
    );

    return {
        sourceEntity,
        targetEntity,
        sourceSchema,
        targetSchema,
        classifications,
    };
};

const getDetailEntityById = async (entityType, entityId): Promise<DetailEntity> => {
    const res = await fetch({
        ...entityUrls.getEntityById,
        params: { entityType, entityId },
    });
    return res.data;
};

const getDetailSchema = async (entityType): Promise<DetailSchema> => {
    const res = await fetch({
        ...schemaUrls.getSchemaDetail,
        params: { entityTypeName: entityType },
    });
    return res.data;
};

const getClassificationSchema = async (classifications: any[]) => {
    const responses = await Promise.all(
        classifications.map((classification) =>
            fetch({
                ...classificationUrls.getClassificationDetail,
                params: { name: classification.name },
            })
        )
    );
    return responses.map((response) => response.data);
};

const getBomSchema = async (bomType: string) => {
    if (bomType === undefined || bomType === null) return { entityType: { attributeOrder: [] }, attributes: {} };
    const { data } = await fetch({
        ...schemaUrls.getSchemaDetail,
        params: { entityTypeName: bomType },
    });
    return data;
};

const fetchBom = async (entityId, viewOption = BOM_VIEW_OPTIONS.LATEST_REVISION) => {
    const {
        data: { data },
    } = await fetch({
        ...entityUrls.getBOMList,
        params: {
            entityId,
        },
        qs: {
            viewOption,
            limit: DEFAULT_CLIENT_SIDE_LIMIT,
        },
    });
    return data;
};

function buildCommonWithDiffRow(
    sourceBom: Bom,
    targetBom: Bom,
    attributes: Attribute[],
    sourceProperties: Record<string, any>,
    targetProperties: Record<string, any>,
    firstLevelGroup: {}
) {
    sourceBom.comparisonResult = {
        result: ComparisonResultType.COMMON_WITH_DIFFERENCES,
        commonWith: {
            rowId: targetBom.rowId,
        },
        differences: {
            attributes: attributes
                .filter((attribute) => sourceProperties[attribute.name] !== targetProperties[attribute.name])
                .map((attribute) => attribute.name),
        },
    };
    targetBom.comparisonResult = {
        result: ComparisonResultType.COMMON_WITH_DIFFERENCES,
        commonWith: {
            rowId: sourceBom.rowId,
        },
        differences: {
            attributes: ['componentQuantity'],
        },
    };
    firstLevelGroup = {
        ...firstLevelGroup,
        [sourceBom.rowId]: ComparisonResultType.COMMON_WITH_DIFFERENCES,
        [targetBom.rowId]: ComparisonResultType.COMMON_WITH_DIFFERENCES,
    };
    return firstLevelGroup;
}

function buildCommonRow(sourceBom: Bom, targetBom: Bom, firstLevelGroup: {}) {
    sourceBom.comparisonResult = {
        result: ComparisonResultType.COMMON,
        commonWith: {
            rowId: targetBom.rowId,
        },
    };
    targetBom.comparisonResult = {
        result: ComparisonResultType.COMMON,
        commonWith: {
            rowId: sourceBom.rowId,
        },
    };
    firstLevelGroup = {
        ...firstLevelGroup,
        [sourceBom.rowId]: ComparisonResultType.COMMON,
        [targetBom.rowId]: ComparisonResultType.COMMON,
    };
    return firstLevelGroup;
}

function allAttributeEqual(
    attributes: unknown[],
    sourceProperties: Record<string, any>,
    targetProperties: Record<string, any>
) {
    return attributes.every((attribute: any) => sourceProperties[attribute.name] === targetProperties[attribute.name]);
}

function combineComparisonResults(
    sourceBomList: Bom[],
    sourceResults: any[],
    firstLevelGroup: {},
    firstLevelSourceBom: Bom[]
) {
    sourceBomList.forEach((sourceBom) => {
        if (sourceBom.level !== 1) {
            const path = sourceBom.path;
            if (path.length > 2) {
                sourceResults.push({
                    ...sourceBom,
                    comparisonResult: {
                        result: firstLevelGroup[path[1]],
                        firstLevelParent: path[1],
                    },
                });
            } else {
                sourceResults.push(sourceBom);
            }
        }
    });
    sourceResults = sourceResults.concat(firstLevelSourceBom);
    return sourceResults;
}
