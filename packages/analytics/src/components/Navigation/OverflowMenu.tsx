/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { IconButton, Menu, MenuItem, styled } from '@mui/material';

const MoreButton = styled(IconButton)(({ theme }) => ({
    borderRadius: 0,
    height: 32,
    width: 32,
    '&:hover': { backgroundColor: '#F4F4F4' },
}));

const StyledMenu = styled(Menu)(({ theme }) => ({
    '& .MuiPaper-root': {
        background: '#F4F4F4',
        boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.22)',
        '& ul': { padding: 0 },
        borderRadius: 0,
    },
}));

const overflowItemStyle = {
    display: 'flex',
    gap: '16px',
    justifyContent: 'space-between',
    '&:hover': {
        backgroundColor: '#E5E5E5',
    },
    '&:focus': {
        border: '2px solid #0F62FE',
    },
};
const OverflowMenu = ({ items, ...props }) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const onItemClick = (onClick) => {
        onClick && onClick();
        handleClose();
    };
    return (
        <>
            <MoreButton
                sx={{
                    backgroundColor: open ? '#F4F4F4' : 'none',
                    boxShadow: open ? '0px 4px 6px rgba(0, 0, 0, 0.26)' : 'none',
                }}
                onClick={handleClick}
            >
                {/* <MoreIcon /> */}...
            </MoreButton>
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                {...props}
            >
                {items.map(({ label, icon, style, onClick, validatePermissions }) => (
                    <MenuItem
                        key={label}
                        disabled={validatePermissions && validatePermissions()?.error}
                        style={overflowItemStyle}
                        onClick={() => onItemClick(onClick)}
                    >
                        {label}
                        {icon}
                    </MenuItem>
                ))}
            </Menu>
        </>
    );
};
export default OverflowMenu;
