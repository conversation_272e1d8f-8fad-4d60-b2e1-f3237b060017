/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { Box, Typography, useTheme, IconButton, FormControlLabel, Switch } from '@mui/material';
import { ArrowLeft } from '@glidesystems/styleguide';
import { Link, useMatch, useNavigate } from 'react-router-dom';
import MainTabs from '../Tabs/MainTabs';
import MainTabItem from '../Tabs/MainTabItem';
import { useAppStore } from '../../store/appStore';

const tabViews = [
    {
        id: 'attributes',
        to: 'attributes',
        label: 'Attributes',
        active: 'attributes',
    },
    {
        id: 'bom',
        to: 'bom',
        label: 'BOM',
        active: 'bom',
    },
];

const NavigationTabs = () => {
    const theme = useTheme();
    const matched = useMatch('analytics/comparison-result/:sourceType/:sourceId/:targetType/:targetId/:view');
    const params = matched?.params;
    const selectedTab = params?.view;
    const [showComparedItems, toggleShowComparedItems] = useAppStore((state) => [
        state.showComparedItems,
        state.toggleShowComparedItems,
    ]);

    const navigate = useNavigate();

    const handleBack = useCallback(() => {
        navigate(-1);
    }, []);

    return (
        <Box
            sx={{
                borderBottom: '2px solid',
                borderColor: theme.palette.glide.stroke.normal.primary,
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    padding: '12px',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        gap: '16px',
                        alignItems: 'center',
                        flexGrow: 1,
                        overflow: 'hidden',
                    }}
                >
                    <IconButton onClick={handleBack}>
                        <ArrowLeft />
                    </IconButton>
                    <Typography variant="title3">Comparison Result</Typography>
                </Box>
                <Box
                    sx={{
                        pt: '2px',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                    }}
                >
                    <FormControlLabel
                        sx={{
                            fontSize: '14px',
                            fontWeight: 400,
                            gap: '8px',
                            color: (theme) => theme.palette.glide.text.normal.inverseTertiary,
                        }}
                        label="Show Compared Items"
                        control={
                            <Switch size="medium" checked={showComparedItems} onChange={toggleShowComparedItems} />
                        }
                    />
                </Box>
            </Box>

            <MainTabs
                value={selectedTab}
                variant="scrollable"
                scrollButtons="auto"
                allowScrollButtonsMobile
                aria-label="Comparison Result navigation tabs"
            >
                {tabViews.map((view) => {
                    return (
                        <MainTabItem
                            key={view.id || view.to}
                            value={view.active || view.to}
                            label={view.label}
                            //@ts-ignore
                            to={view.to}
                            component={Link}
                        />
                    );
                })}
            </MainTabs>
        </Box>
    );
};

export default NavigationTabs;
