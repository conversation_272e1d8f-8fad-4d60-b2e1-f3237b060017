/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Chip } from '@mui/material';

const StatusMapping = {
    start: {
        variant: 'status-initial',
    },
    inProgress: {
        variant: 'status-info',
    },
    released: {
        variant: 'status-success',
    },
    obsoleted: {
        variant: 'status-secondary',
    },
    superseded: {
        variant: 'status-warning',
    },
};

const getStatus = (state: any) => {
    if (state?.isObsoleted) {
        return {
            status: 'obsoleted',
            label: 'Obsoleted',
        };
    }

    if (state?.isOfficial) {
        return {
            status: 'released',
            label: 'Released',
        };
    }

    if (state?.isSuperseded) {
        return {
            status: 'superseded',
            label: 'Superseded',
        };
    }

    return {
        status: 'start',
        label: 'Start',
    };
};

const StatusCellRenderer = ({ data }) => {
    const { state } = data;
    const { status } = getStatus(state);
    const label = state?.name;
    return label ? <Chip size="small" label={label} variant={StatusMapping[status].variant} /> : null;
};
export default StatusCellRenderer;
