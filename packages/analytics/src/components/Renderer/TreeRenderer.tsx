/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import get from 'lodash/get';
import { memo } from 'react';

export const LinkToDetail = ({ type, id, displayText }) => (
    <Link style={{ textDecoration: 'none' }} to={`/detail/${type}/${id}/properties`}>
        {displayText}
    </Link>
);

const TreeRenderer = (props) => {
    const root = get(props.data, 'uiStates.isRoot');
    const { typePath, idPath } = props;
    const type = get(props.data, typePath);
    const id = get(props.data, idPath);
    const textStyles = get(props, 'textStyles', {});
    const containerStyles = get(props, 'containerStyles', {});
    return (
        <Box
            sx={{
                flexDirection: 'row',
                display: 'flex',
                alignItems: 'center',
                alignSelf: 'center',
                '&.ag-selection-checkbox ': {
                    mr: '0px',
                },
                color: 'red',
                ...containerStyles,
                height: '34px',
            }}
        >
            <Box
                sx={{
                    backgroundColor: root ? '#52C41A' : '#2F54EB',
                    width: '8px',
                    height: '8px',
                    borderRadius: '10px',
                    ml: '6px',
                    mr: '6px',
                }}
            ></Box>
            <Typography
                sx={{
                    '& a': {
                        fontSize: '14px',
                        lineHeight: '18px',
                        fontWeight: 400,
                        color: (theme) => theme.palette.info.main,
                        ...textStyles,
                    },
                }}
            >
                <LinkToDetail id={id} type={type} displayText={props.value} />
            </Typography>
        </Box>
    );
};

export default memo(TreeRenderer);
