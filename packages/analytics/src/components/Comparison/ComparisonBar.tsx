/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, styled, Typography } from '@mui/material';
import React from 'react';
import { ComparisonBarProps } from '../../model';

const Wrapper = styled('div')(({ theme }) => ({
    '& .label': {
        fontSize: '14px',
        fontWeight: 600,
        color: theme.palette.glide.text.normal.inverseSecondary,
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
    },
    '& .name': {
        fontSize: '12px',
        fontWeight: 500,
        color: theme.palette.glide.text.normal.inverseSecondary,
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
    },
    '& .compare-bar': {
        display: 'flex',
        margin: '2px 0',
        '& .bar': {
            height: '8px',
            cursor: 'pointer',
        },
        borderRadius: '100px',
        overflow: 'hidden',
        alignItems: 'center',
    },
}));

const ComparisionBar = ({ source, target, label, onClick }: ComparisonBarProps) => {
    const total = source.total + (target?.total || 0);
    const sourcePercent = (source.total / total) * 100;

    const isEmpty = source?.total === 0 && target?.total === 0;

    return (
        <Wrapper>
            <Typography className="label">
                {label} ({total})
            </Typography>
            <Box className="compare-bar">
                <Box
                    className="bar sourceBar"
                    sx={{
                        width: `${sourcePercent}%`,
                        backgroundColor: source.color,
                    }}
                />
                {target && (
                    <Box
                        className="bar targetBar"
                        sx={{
                            backgroundColor: isEmpty ? '#434343' : target.color,
                            flexGrow: 1,
                        }}
                    />
                )}
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                {source?.name && (
                    <Typography className="name">
                        {source.name} ({source.total})
                    </Typography>
                )}
                {target?.name && (
                    <Typography className="name">
                        {target.name} ({target.total})
                    </Typography>
                )}
            </Box>
        </Wrapper>
    );
};

export default ComparisionBar;
