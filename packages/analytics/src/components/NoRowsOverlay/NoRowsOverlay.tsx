/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Box } from '@mui/material';

const NoRowsOverlay = (params) => {
    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                p: 2,
                opacity: 0.8,
            }}
        >
            <div>
                <svg width="163" height="75" viewBox="0 0 163 75" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M21.3041 61.8613V50.4964H0V46.6204L19.9147 18.5037H26.597V45.9635H34.3379V50.4964H26.597V61.8613H21.3041ZM5.62375 45.9635H21.3041V29.7372C21.3041 28.5985 21.3261 27.438 21.3702 26.2555C21.4584 25.073 21.5246 23.9124 21.5687 22.7737H21.4364C20.9071 23.8686 20.2234 25.0949 19.3854 26.4526C18.5473 27.8102 17.7754 28.9927 17.0697 30L5.62375 45.9635Z"
                        fill="white"
                    />
                    <path
                        d="M149.966 61.8613V50.4964H128.662V46.6204L148.577 18.5037H155.259V45.9635H163V50.4964H155.259V61.8613H149.966ZM134.286 45.9635H149.966V29.7372C149.966 28.5985 149.988 27.438 150.032 26.2555C150.121 25.073 150.187 23.9124 150.231 22.7737H150.098C149.569 23.8686 148.886 25.0949 148.047 26.4526C147.209 27.8102 146.438 28.9927 145.732 30L134.286 45.9635Z"
                        fill="white"
                    />
                    <g clipPath="url(#clip0_1032_2972)">
                        <path
                            d="M121.065 37.4832C121.065 58.2064 104.138 75 83.2512 75C71.3689 75 60.7392 69.5589 53.8333 61.0278C48.5861 54.6126 45.4717 46.4174 45.4717 37.5168C45.4717 16.7935 62.398 0 83.2512 0C92.2221 0 100.448 3.09001 106.948 8.26243C115.547 15.1478 121.065 25.6605 121.065 37.4832Z"
                            fill="#DFDFDF"
                        />
                        <path
                            d="M115.309 31.807C115.309 52.5302 98.3829 69.2902 77.4959 69.2902C68.525 69.2902 60.2988 66.2002 53.7991 60.9942C48.5519 54.579 45.4375 46.3838 45.4375 37.4832C45.4714 16.7936 62.3976 0 83.2508 0C92.2218 0 100.448 3.09001 106.948 8.26243C112.195 14.7112 115.309 22.9064 115.309 31.807Z"
                            fill="white"
                        />
                        <path
                            d="M96.7241 40.3718C96.6564 41.1107 96.3178 41.816 95.81 42.3198C95.3023 42.8236 94.6252 43.1595 93.8466 43.1931L92.3571 43.2939C78.0036 44.1335 63.6501 44.1671 49.2966 43.4282L47.8409 43.361C45.8098 43.361 43.9817 42.0847 43.3047 40.1703L43 39.297V39.2634C43.0339 39.3642 43.1016 39.4985 43.1354 39.5993C43.2031 39.7 43.237 39.8008 43.3385 39.9016C43.3385 39.9016 43.3385 39.9351 43.3724 39.9351C43.4401 40.0359 43.5078 40.1031 43.5755 40.1703C43.6771 40.271 43.7448 40.3718 43.8463 40.439C43.914 40.5061 44.0156 40.5733 44.0833 40.6405C44.2864 40.7748 44.4895 40.8756 44.6926 40.9763H44.7265C44.828 41.0099 44.9635 41.0435 45.065 41.0771C45.1666 41.1107 45.2681 41.1107 45.3697 41.1443C45.4712 41.1779 45.6066 41.1779 45.7082 41.1779L47.1639 41.245C61.5174 41.984 75.8709 41.9168 90.2244 41.1107L91.7139 41.0099C92.4925 40.9428 93.1695 40.6405 93.6773 40.1367C94.1851 39.6329 94.5236 38.9611 94.5914 38.1886C95.3023 29.456 95.3023 20.7233 94.5914 11.9907V11.7892C94.5575 11.2182 94.3205 10.6808 94.0159 10.2106C93.982 10.177 93.9481 10.1098 93.9143 10.0762C93.8466 10.009 93.7789 9.90828 93.7112 9.84111C93.3388 9.47165 92.8987 9.20295 92.3909 9.0686L92.8987 9.20295C95.0653 9.77393 96.5887 11.6884 96.7918 13.9051C96.7918 13.9051 96.7918 13.9051 96.7918 13.9387V14.1403C97.435 22.8729 97.435 31.6391 96.7241 40.3718Z"
                            fill="#323436"
                        />
                        <path
                            d="M93.8467 43.193L92.3571 43.2938C78.0375 44.1335 63.6501 44.2007 49.2966 43.4617L47.841 43.3946C46.2838 43.2938 45.0651 42.0847 44.9297 40.5732V40.3045C44.2188 31.6391 44.2188 22.9064 44.9297 14.241V14.0059C45.0651 12.4944 46.2838 11.2853 47.8071 11.1845C63.1424 10.2777 78.5114 10.2777 93.8467 11.1845C95.37 11.2853 96.5887 12.4944 96.7241 14.0059V14.1738C97.435 22.9064 97.435 31.6391 96.7241 40.3717C96.5887 41.8831 95.37 43.0923 93.8467 43.193Z"
                            fill="#323436"
                        />
                        <path
                            d="M91.6803 41.0435L90.1908 41.1442C75.8712 41.9839 61.4838 42.0511 47.1303 41.3122L45.6746 41.245C44.1174 41.1442 42.8987 39.9351 42.7633 38.4237L42.7295 38.1886C42.0186 29.5231 42.0186 20.8241 42.7295 12.1586L42.7633 11.8899C42.8987 10.3785 44.1174 9.16933 45.6408 9.06857C60.976 8.16171 76.3451 8.16171 91.6803 9.06857C93.2037 9.16933 94.4224 10.3785 94.5578 11.8899V12.0578C95.2687 20.7905 95.2687 29.5231 94.5578 38.2558C94.4224 39.7336 93.2037 40.9427 91.6803 41.0435Z"
                            fill="#424446"
                        />
                        <path
                            d="M63.0074 58.7775C62.9397 56.3256 65.1063 53.6722 69.7441 53.6722C74.3819 53.6722 76.5484 56.3256 76.4807 58.7775H63.0074Z"
                            fill="#424446"
                        />
                        <path
                            d="M61.7548 29.4559C61.5517 29.4559 61.3824 29.3888 61.2132 29.2208L53.7656 21.7981C53.4609 21.4958 53.4609 21.0256 53.7656 20.7233C54.0703 20.421 54.5442 20.421 54.8489 20.7233L62.3303 28.146C62.635 28.4483 62.635 28.9185 62.3303 29.2208C62.1272 29.3552 61.9579 29.4559 61.7548 29.4559Z"
                            fill="white"
                        />
                        <path
                            d="M54.4762 29.4559C54.2731 29.4559 54.1038 29.3888 53.9346 29.2208C53.6299 28.9185 53.6299 28.4483 53.9346 28.146L61.416 20.7233C61.7207 20.421 62.1946 20.421 62.4993 20.7233C62.8039 21.0256 62.8039 21.4958 62.4993 21.7981L54.984 29.2208C54.8486 29.3552 54.6455 29.4559 54.4762 29.4559Z"
                            fill="white"
                        />
                        <path
                            d="M82.6757 29.4559C82.4726 29.4559 82.3033 29.3888 82.1341 29.2208L74.6865 21.7981C74.3818 21.4958 74.3818 21.0256 74.6865 20.7233C74.9912 20.421 75.4651 20.421 75.7698 20.7233L83.2512 28.146C83.5559 28.4483 83.5559 28.9185 83.2512 29.2208C83.0481 29.3552 82.8788 29.4559 82.6757 29.4559Z"
                            fill="white"
                        />
                        <path
                            d="M75.3971 29.4559C75.194 29.4559 75.0247 29.3888 74.8555 29.2208C74.5508 28.9185 74.5508 28.4483 74.8555 28.146L82.3369 20.7233C82.6416 20.421 83.1155 20.421 83.4202 20.7233C83.7248 21.0256 83.7248 21.4958 83.4202 21.7981L75.9387 29.2208C75.7695 29.3552 75.5664 29.4559 75.3971 29.4559Z"
                            fill="white"
                        />
                    </g>
                    <defs>
                        <clipPath id="clip0_1032_2972">
                            <rect width="78.8427" height="75" fill="white" transform="translate(42.2217)" />
                        </clipPath>
                    </defs>
                </svg>
            </div>
            {params.message}
        </Box>
    );
};

export default NoRowsOverlay;
