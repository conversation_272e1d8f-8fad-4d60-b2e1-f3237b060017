/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled } from '@mui/material/styles';
import { Tab, type TabProps } from '@mui/material';

const TabItem = styled(Tab)<TabProps>(({ theme }) => ({
    fontSize: '14px',
    minHeight: '19px',
    marginRight: '2px',
    padding: '12px 40px 8px 12px',
    textTransform: 'none',
    color: theme.palette.glide.text.normal.inversePrimary,
    '&.Mui-selected': {
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 700,
    },
    '&.Mui-disabled': {
        color: '#C2C2C2',
        borderColor: '#C2C2C2',
    },
}));

export default TabItem;
