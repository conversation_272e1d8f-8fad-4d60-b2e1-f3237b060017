/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const ATTRIBUTE_GROUP = {
    COMMON: 'Common Attributes - Similar',
    COMMON_WITH_DIFFERENCES: 'Common Attributes - With Differences',
    UNIQUE_ON: 'Unique Attributes -',
};

export const CLASSIFICATION_GROUP = {
    COMMON: 'Common Classifications - Similar',
    COMMON_WITH_DIFFERENCES: 'Common Classifications - With Differences',
    UNIQUE_ON: 'Unique Classifications -',
};

export enum BOM_VIEW_OPTIONS {
    LATEST_OFFICIAL = 'LATEST_OFFICIAL',
    LATEST_REVISION = 'LATEST_REVISION',
}

export const EXCLUDED_BOM_FIELDS = ['description', 'type', 'name', 'title'];
