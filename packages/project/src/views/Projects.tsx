/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, InputAdornment, TextField, Typography } from '@mui/material';
import { CloseIcon, SearchIcon } from '@glidesystems/styleguide';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useRef, useState } from 'react';
import CreateProject from '../components/CreateProject';
import { useSearchParams } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';
import ViewTypeSwitch, { ViewType } from '../components/ViewTypeSwitch';
import ProjectList from './components/ProjectList';
import ProjectTile from './components/ProjectTile';

const Projects = () => {
    const [searchText, setSearchText] = useState('');
    const [searchParams, setSearchParams] = useSearchParams();
    const gridRef = useRef<AgGridReact>(null);
    const viewType = searchParams.get('viewType') === ViewType.Tile ? ViewType.Tile : ViewType.List;
    const setViewType = (value) => {
        searchParams.set('viewType', value);
        setSearchParams(searchParams);
    };
    const refreshData = useCallback(() => {
        if (viewType === ViewType.List && gridRef.current) {
            gridRef.current.api.refreshServerSide();
        }
    }, [viewType]);
    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearchText(value);
    }, []);

    return (
        <Box
            sx={{
                marginTop: '52px',
                display: 'flex',
                flexDirection: 'column',
                height: 'calc(100vh - 52px)',
                '& .searchBox': { maxWidth: 286 },
                '& .headerContainer': {
                    padding: '12px 16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    borderBottom: '1px solid #E9E9E9',
                },
                '& .actionContainer': {
                    display: 'flex',
                    gap: '16px',
                },
            }}
        >
            <Box
                className="headerContainer"
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: { xs: 1.5, sm: 2 },
                }}
            >
                <Box
                    className="titleActionsRow"
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                    }}
                >
                    <Typography variant="title3">Projects</Typography>
                    <Box
                        className="actionContainer"
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: { xs: 1, sm: 2 },
                        }}
                    >
                        {/* Desktop search inline before Create */}
                        <TextField
                            className="searchBox"
                            size="small"
                            value={searchText}
                            onChange={onSearchChanged}
                            placeholder="Type to search"
                            sx={{ display: { xs: 'none', sm: 'block' }, minWidth: 240 }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon />
                                    </InputAdornment>
                                ),
                                endAdornment: (
                                    <InputAdornment
                                        position="end"
                                        style={{
                                            visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => setSearchText('')}
                                    >
                                        <CloseIcon sx={{ width: '16px', height: '16px' }} />
                                    </InputAdornment>
                                ),
                            }}
                        />
                        <CreateProject onRefresh={refreshData} />
                        <ViewTypeSwitch viewType={viewType} setViewType={setViewType} />
                    </Box>
                </Box>

                <TextField
                    className="searchBox"
                    size="small"
                    value={searchText}
                    onChange={onSearchChanged}
                    fullWidth
                    placeholder="Type to search"
                    sx={{ display: { xs: 'block', sm: 'none' } }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                        endAdornment: (
                            <InputAdornment
                                position="end"
                                style={{
                                    visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                    cursor: 'pointer',
                                }}
                                onClick={() => setSearchText('')}
                            >
                                <CloseIcon sx={{ width: '16px', height: '16px' }} />
                            </InputAdornment>
                        ),
                    }}
                />
            </Box>
            <Box sx={{ flex: 1, minHeight: 0 }}>
                {viewType === ViewType.List ? (
                    <ProjectList gridRef={gridRef} searchQuery={searchText} />
                ) : (
                    <ProjectTile searchQuery={searchText} />
                )}
            </Box>
        </Box>
    );
};

export default Projects;
