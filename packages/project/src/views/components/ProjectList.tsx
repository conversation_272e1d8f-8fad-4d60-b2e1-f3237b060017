/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { RefObject, useCallback, useEffect } from 'react';
import { Box, Chip } from '@mui/material';
import { AgGridReact } from '@ag-grid-community/react';
import type { GridOptions } from '@ag-grid-community/core';
import {
    fetch as apiFetch,
    buildContainsQuery,
    buildOrOperatorQuery,
    EntityDetail,
    entityUrls,
    SYSTEM_ENTITY_TYPE,
} from '@glidesystems/api';
import AssigneeRenderer from '../../components/AssigneeRenderer';
import moment from 'moment';
import {
    buildQueryBasedOnFilter,
    buildSortParams,
    DEFAULT_CELL_WIDTH,
    EntityNameRenderer,
    formatDateTime,
    LoadingOverlay,
    NoRowsOverlay,
    tableIcons,
    tableStyles,
} from '@glidesystems/styleguide';

const gridOptions: GridOptions = {
    headerHeight: 34,
    rowHeight: 36,
    floatingFiltersHeight: 36,
    columnDefs: [
        {
            field: 'properties.name',
            headerName: 'Name',
            cellRenderer: EntityNameRenderer,
            flex: 2,
            filter: 'agTextColumnFilter',
            minWidth: DEFAULT_CELL_WIDTH,
            pinned: 'left',
            headerTooltip: 'Project Name',
            floatingFilter: true,
        },
        {
            field: 'properties.description',
            headerName: 'Description',
            flex: 1,
            filter: 'agTextColumnFilter',
            minWidth: DEFAULT_CELL_WIDTH,
            headerTooltip: 'Project description',
            floatingFilter: true,
        },
        {
            field: 'relationInfo.relation.ACCESSOR:Agent[id,name,email,type]',
            headerName: 'Assignees',
            flex: 2,
            filter: 'agTextColumnFilter',
            floatingFilter: true,
            sortable: false,
            minWidth: DEFAULT_CELL_WIDTH,
            headerTooltip: 'Assignees',
            valueGetter: ({ data }) => data.relationInfo?.['relation.ACCESSOR:Agent[id,name,email,type]'] || [],
            cellRendererParams: {
                fullHeight: true,
            },
            cellRenderer: AssigneeRenderer,
        },
        {
            field: 'state.name',
            headerName: 'Status',
            cellRenderer: ({ data }) => {
                if (!data?.state) {
                    return null;
                }
                const { state } = data;
                const label = state?.name;
                return label ? (
                    <Chip size="small" label={label} variant={label === 'Active' ? 'status-success' : 'status-error'} />
                ) : null;
            },
            flex: 1,
            filter: false,
            minWidth: DEFAULT_CELL_WIDTH,
            headerTooltip: 'Current state of the entity',
        },
        {
            field: 'updatedAt',
            headerName: 'Updated At',
            valueFormatter: (params) => {
                return formatDateTime(params.value);
            },
            valueGetter: (params) => {
                return moment.utc(params.data?.updatedAt).toDate();
            },
            editable: false,
            minWidth: DEFAULT_CELL_WIDTH,
            flex: 1,
            filter: 'agDateColumnFilter',
            enableRowGroup: false,
        },
    ],
    loadingOverlayComponent: LoadingOverlay,
    defaultColDef: {
        minWidth: 100,
        sortable: true,
        resizable: true,
        filter: true,
    },
    cacheBlockSize: 50,
    suppressColumnVirtualisation: true,
    serverSideInfiniteScroll: true,
    suppressMenuHide: true,
    icons: tableIcons,
    rowSelection: 'multiple',
    getRowId: (params) => params.data.id,
    suppressRowClickSelection: true,
    rowStyle: {
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
    },
    rowModelType: 'serverSide',
    noRowsOverlayComponent: NoRowsOverlay,
    noRowsOverlayComponentParams: {
        defaultMessage: 'No projects found',
    },
};

const getDataSource = (searchText?: string) => {
    const buildParams = (params) => {
        let queryParams = {
            fields: 'relation.ACCESSOR:Agent[id,name,email,type]',
            offset: params.startRow || 0,
            limit: 50,
            ...buildSortParams(params.sortModel),
        };
        const filterModel = params.filterModel;
        if (filterModel && Object.keys(filterModel).length > 0) {
            const filterConditions = [];

            if (searchText) {
                filterConditions.push(
                    buildOrOperatorQuery([
                        buildContainsQuery('name', searchText),
                        buildContainsQuery('description', searchText),
                    ])
                );
            }
            queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
        } else if (searchText) {
            queryParams['query'] = JSON.stringify(
                buildOrOperatorQuery([
                    buildContainsQuery('name', searchText),
                    buildContainsQuery('description', searchText),
                ])
            );
        }
        return queryParams;
    };
    return {
        getRows: (params) => {
            apiFetch({
                ...entityUrls.getListEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.PROJECT },
                qs: buildParams(params.request),
            })
                .then((response) => {
                    const {
                        status,
                        data: { data, pageInfo },
                    } = response;
                    if (status !== 200) {
                        params.fail();
                        return;
                    }
                    params.success({
                        rowData: data,
                        rowCount: pageInfo.total || data.length,
                    });
                })
                .catch(() => {
                    params.fail();
                })
                .finally(() => {
                    params.api.hideOverlay();
                    if (params.api.getDisplayedRowCount() === 0) {
                        params.api.showNoRowsOverlay();
                    }
                });
        },
    };
};

export default function ProjectList({
    searchQuery,
    gridRef,
}: {
    searchQuery: string;
    gridRef: RefObject<AgGridReact>;
}) {
    const handleSetDataSource = useCallback(({ api }, searchQuery) => {
        const dataSource = getDataSource(searchQuery);
        api.setServerSideDatasource(dataSource);
    }, []);

    const refreshData = useCallback(
        (searchQuery: string) => handleSetDataSource({ api: gridRef.current?.api }, searchQuery),
        [gridRef, handleSetDataSource]
    );

    useEffect(() => {
        if (gridRef.current?.api) {
            refreshData(searchQuery);
        }
    }, [searchQuery, refreshData]);

    return (
        <Box sx={{ ...tableStyles, height: '100%' }}>
            <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
                <AgGridReact
                    {...gridOptions}
                    ref={gridRef}
                    onGridReady={(props) => handleSetDataSource(props, searchQuery)}
                />
            </div>
        </Box>
    );
}
