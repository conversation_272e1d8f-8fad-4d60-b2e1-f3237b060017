/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { RichTextEditor, EntitySelect, formatSystemDate, formatSystemDateTime } from '@glidesystems/styleguide';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import { Checkbox, FormControlLabel, TextField as Text, Typography } from '@mui/material';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { TextField } from 'formik-mui';
import { Field, FormikValues } from 'formik';
import {
    SYSTEM_ATTRIBUTE,
    AttributeType,
    Schema,
    RelationType,
    Attribute,
    RELATION_DIRECTION,
    buildCascadedQueries,
    buildAndOperatorQuery,
} from '@glidesystems/api';
import UnitOfMeasureField from '../components/UnitOfMeasure/UnitOfMeasureField';
import Autocomplete from '@mui/material/Autocomplete';
import { DateArrayInput, MultiValueInput } from '../components/ArrayAttribute';
import filter from 'lodash/filter';
import { useGlobalConfig } from '@glidesystems/caching-store';
import { CascadingList } from '../components/CascadingList';

const folderPaths = [{ name: 'OwnerFolder', label: 'Owner Folder' }];

export const buildRequireMessage = (displayName) => {
    return `${displayName} is required`;
};

export const buildMinMessage = (displayName, minValue) => {
    return `${displayName} must be greater than or equal to ${minValue}`;
};

export const buildMaxMessage = (displayName, maxValue) => {
    return `${displayName} must be less than or equal to ${maxValue}`;
};

export const DATE_FORMAT = 'yyyy-MM-dd';

const transformValueToOption = (value, attributes) => {
    return (value ? (value || '').replace(' ', '').split(',') : []).map((val) => ({
        name: val,
        displayName: attributes.find((att) => att.name === val)?.displayName || val,
    }));
};

export const isRelationRequired = (r: RelationType) =>
    r.visible && r.required && r.direction === RELATION_DIRECTION.OUTGOING;
export const isValidAttributeToRender = (attribute: Attribute) =>
    attribute && attribute.visible && isEmpty(attribute.identifier) && attribute.mutable;

export const getRequireRelations = (schemaDetail: Schema) => {
    return filter(schemaDetail?.relationTypes, isRelationRequired) || [];
};

export const sortAndGroupAttributes = (schema: Schema, excludedAttributes?: Record<string, Attribute>) => {
    let sortedKeys = {};
    const attributeOrder = get(schema, 'entityType.attributeOrder') ?? [];
    const attributes = get(schema, 'attributes', {});
    let sortedAttributes = [];

    attributeOrder.forEach((attribute: any) => {
        const isGroup = attribute.includes(':');
        if (isGroup) {
            const [groupName, groupString] = attribute.split(':');
            const groupAttributes = groupString?.split(',') || [];
            let sortedGroupAttributes = [];
            groupAttributes.forEach((groupAttribute) => {
                const schemaAttribute = attributes[groupAttribute];
                sortedKeys[groupAttribute] = true;
                if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[groupAttribute]) {
                    sortedGroupAttributes.push(schemaAttribute);
                }
            });
            if (sortedGroupAttributes.length > 0) {
                sortedAttributes.push({
                    type: AttributeType.GROUP,
                    attributes: sortedGroupAttributes,
                    name: groupName,
                });
            }
        } else {
            const schemaAttribute = attributes[attribute];
            sortedKeys[attribute] = true;
            if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[schemaAttribute.name]) {
                sortedAttributes.push(attributes[attribute]);
            }
        }
    });
    Object.values(get(schema, 'attributes', {})).forEach((attribute: any) => {
        if (
            !sortedKeys[attribute.name] &&
            isValidAttributeToRender(attribute) &&
            !excludedAttributes?.[attribute.name]
        ) {
            sortedAttributes.push(attribute);
        }
    });
    return sortedAttributes;
};

interface AttributeWithOnChange extends Attribute {
    onChange?: (value: any) => void;
}

export const buildFormItem = ({
    attribute,
    setFieldValue,
    size = 'medium',
    attributes = [],
    values = {},
}: {
    attribute: AttributeWithOnChange;
    setFieldValue: (name: string, value: any, shouldValidate?: boolean) => void;
    size?: 'small' | 'medium';
    values?: FormikValues;
    attributes?: Attribute[];
}) => {
    /**
     * url, method, datasource & multiSelect are properties supports for relation
     */
    const { id, displayName, name, description, nullable, unitOfMeasure } = attribute;
    const isRequired = isNil(nullable) ? false : !Boolean(nullable);
    const hasUnitOfMeasure = unitOfMeasure && !isEmpty(unitOfMeasure);
    if (attribute.name === SYSTEM_ATTRIBUTE.REQUIRED_INFORMATION) {
        return (
            <Grid item xs={12} key={id}>
                <Field name={name}>
                    {({ field, form, meta: { error, touched } }) => {
                        const isErr = touched && !!error;
                        const value = transformValueToOption(field.value, attributes);
                        return (
                            <Autocomplete
                                sx={{ '& label': { background: '#FFFFFF' } }}
                                options={attributes}
                                multiple
                                value={value}
                                getOptionLabel={(option) => option.displayName}
                                onChange={(e, newValue) => {
                                    const stringValue = newValue.map((val) => val.name).join(',');
                                    setFieldValue(name, stringValue);
                                    if (attribute.onChange) {
                                        attribute.onChange(stringValue);
                                    }
                                }}
                                renderInput={(params) => (
                                    <Text
                                        {...params}
                                        required
                                        error={isErr}
                                        helperText={isErr ? error : description}
                                        InputLabelProps={{ shrink: true }}
                                        size="medium"
                                        label={displayName}
                                        onBlur={() => form.setFieldTouched(name, true, true)}
                                    />
                                )}
                            />
                        );
                    }}
                </Field>
            </Grid>
        );
    }
    if (attribute.richText) {
        return (
            <Grid item xs={12} key={id}>
                <Field
                    fullWidth
                    variant="outlined"
                    id={id}
                    label={displayName}
                    name={name}
                    helperText={description}
                    rows={4}
                    disabled={false}
                    required={isRequired}
                    component={RichTextEditor}
                    size={size}
                />
            </Grid>
        );
    }

    switch (attribute.type) {
        case AttributeType.INTEGER:
        case AttributeType.LONG:
        case AttributeType.FLOAT:
            return hasUnitOfMeasure ? (
                <Grid item xs={12} key={id}>
                    <Grid container spacing={2}>
                        <Grid item xs={6}>
                            <Field
                                fullWidth
                                variant="outlined"
                                component={TextField}
                                label={displayName}
                                required={isRequired}
                                name={attribute.name}
                                disabled={false}
                                helperText={attribute.description}
                                InputLabelProps={{ shrink: true }}
                                size={size}
                            />
                        </Grid>

                        <UnitOfMeasureField attribute={attribute} size={size} />
                    </Grid>
                </Grid>
            ) : (
                <Grid item key={id} xs={12}>
                    <Field
                        fullWidth
                        variant="outlined"
                        component={TextField}
                        label={displayName}
                        required={isRequired}
                        name={attribute.name}
                        disabled={false}
                        helperText={attribute.description}
                        InputLabelProps={{ shrink: true }}
                        size={size}
                    />
                </Grid>
            );

        case AttributeType.BOOLEAN:
            return (
                <Grid item key={id} xs={12}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={field.value}
                                                onChange={(e: any) => setFieldValue(name, e.target.checked)}
                                                name={name}
                                                required={isRequired}
                                            />
                                        }
                                        label={displayName}
                                    />
                                    {isErr && (
                                        <Typography
                                            color="error"
                                            sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                        >
                                            {error}
                                        </Typography>
                                    )}
                                </>
                            );
                        }}
                    </Field>
                </Grid>
            );

        case AttributeType.STRING: {
            const dataType = get(attribute, ['constraint', 'dataType'], null);
            if (dataType) {
                const constraintCascadingAttrs = get(attribute, ['constraint', 'cascadingAttrs']);
                const cascadingAttributes = constraintCascadingAttrs ? Object.keys(constraintCascadingAttrs) : [];

                const cascadedQueries = buildCascadedQueries(attributes, name, values);
                const additionalQuery = cascadedQueries.length > 0 ? buildAndOperatorQuery(cascadedQueries) : null;
                return (
                    <Grid item xs={12} key={id}>
                        <Field name={name} helperText={description}>
                            {({ field, form, meta: { error, touched } }) => {
                                const isErr = touched && !!error;
                                return (
                                    <>
                                        <EntitySelect
                                            label={displayName}
                                            entityType={dataType}
                                            onChange={(newValue) => {
                                                setFieldValue(name, newValue.value);
                                                cascadingAttributes.forEach((cascadedAttribute) => {
                                                    setFieldValue(cascadedAttribute, null);
                                                });
                                            }}
                                            defaultValue={field.value}
                                            isRequired={isRequired}
                                            onBlur={() => form.setFieldTouched(name)}
                                            additionalQuery={additionalQuery}
                                        />
                                        {isErr && (
                                            <Typography
                                                color="error"
                                                sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                            >
                                                {error}
                                            </Typography>
                                        )}
                                    </>
                                );
                            }}
                        </Field>
                    </Grid>
                );
            }

            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (enumRange.length > 0) {
                return (
                    <Grid item xs={12} key={id}>
                        <Field
                            size={size}
                            fullWidth
                            select
                            type="text"
                            variant="outlined"
                            name={name}
                            label={displayName}
                            required={isRequired}
                            helperText={description}
                            component={TextField}
                            InputLabelProps={{ shrink: true }}
                            disabled={false}
                            onChange={(e) => {
                                setFieldValue(name, e.target.value);
                            }}
                        >
                            {enumRange.map((enumValue) => (
                                <MenuItem value={enumValue} key={enumValue}>
                                    {enumValue}
                                </MenuItem>
                            ))}
                        </Field>
                    </Grid>
                );
            }

            if (attribute.name === 'fileLocation') {
                return (
                    <Grid item key={id} sx={{ width: '100%' }}>
                        <Grid item xs={12}>
                            <Field
                                size={size}
                                fullWidth
                                select
                                type="text"
                                variant="outlined"
                                name="filePath"
                                label="Folder Path"
                                required={isRequired}
                                component={TextField}
                                InputLabelProps={{ shrink: true }}
                                helperText={description}
                            >
                                {folderPaths.map((folder) => (
                                    <MenuItem value={folder.name} key={folder.name}>
                                        {folder.label}
                                    </MenuItem>
                                ))}
                            </Field>
                        </Grid>
                    </Grid>
                );
            } else {
                return (
                    <Grid item xs={12} key={id}>
                        <Field
                            size={size}
                            fullWidth
                            variant="outlined"
                            component={TextField}
                            required={isRequired}
                            label={displayName}
                            name={name}
                            helperText={description}
                            disabled={false}
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                );
            }
        }
        case AttributeType.TEXT: {
            return (
                <Grid item xs={12} key={id}>
                    <Field
                        size={size}
                        fullWidth
                        multiline
                        variant="outlined"
                        component={TextField}
                        required={isRequired}
                        label={displayName}
                        name={name}
                        helperText={description}
                        disabled={false}
                        InputLabelProps={{ shrink: true }}
                        sx={{
                            textarea: {
                                resize: 'vertical',
                                width: '100%',
                                padding: '0px !important',
                                minHeight: '50px',
                            },
                        }}
                    />
                </Grid>
            );
        }
        case AttributeType.DATE:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DatePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatSystemDate(newValue), true);
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateFormat()}
                                        disabled={false}
                                        renderInput={(params) => (
                                            <Text
                                                //@ts-ignore
                                                size={size}
                                                {...params}
                                                error={isErr}
                                                InputLabelProps={{ shrink: true }}
                                                required={isRequired}
                                                helperText={isErr ? error : description}
                                                fullWidth
                                                onBlur={() => form.setFieldTouched(name)}
                                            />
                                        )}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.DATE_TIME:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DateTimePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatSystemDateTime(newValue));
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                                        renderInput={(params) => {
                                            return (
                                                <Text
                                                    //@ts-ignore
                                                    size={size}
                                                    InputLabelProps={{ shrink: true }}
                                                    {...params}
                                                    error={isErr}
                                                    required={isRequired}
                                                    helperText={isErr ? error : description}
                                                    fullWidth
                                                    onBlur={() => form.setFieldTouched(name)}
                                                />
                                            );
                                        }}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );

        case AttributeType.DATE_ARRAY:
        case AttributeType.DATE_TIME_ARRAY:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <DateArrayInput
                                    isDateTime={attribute.type === AttributeType.DATE_TIME_ARRAY}
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    error={isErr ? error : ''}
                                    required={isRequired}
                                    helperText={description}
                                    onBlur={() => form.setFieldTouched(name)}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.STRING_ARRAY:
            const dataType = get(attribute, ['constraint', 'dataType'], null);
            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (dataType) {
                return (
                    <Grid item xs={12} key={id}>
                        <Field name={name} helperText={description}>
                            {({ field, form, meta: { error, touched } }) => {
                                const isErr = touched && !!error;
                                return (
                                    <>
                                        <EntitySelect
                                            label={displayName}
                                            entityType={dataType}
                                            onChange={(newValue) => {
                                                setFieldValue(
                                                    name,
                                                    newValue?.map(({ value }) => value)
                                                );
                                            }}
                                            isMulti
                                            defaultValue={field.value}
                                            isRequired={isRequired}
                                            onBlur={() => form.setFieldTouched(name)}
                                        />
                                        {isErr && (
                                            <Typography
                                                color="error"
                                                sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                            >
                                                {error}
                                            </Typography>
                                        )}
                                    </>
                                );
                            }}
                        </Field>
                    </Grid>
                );
            }
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <MultiValueInput<string>
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    error={isErr ? error : ''}
                                    helperText={description}
                                    onBlur={() => {
                                        form.setFieldTouched(name);
                                    }}
                                    enumRange={enumRange}
                                    required={isRequired}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.INTEGER_ARRAY:
        case AttributeType.FLOAT_ARRAY:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <MultiValueInput
                                    type={attribute.type as AttributeType.INTEGER_ARRAY | AttributeType.FLOAT_ARRAY}
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    error={isErr ? error : ''}
                                    helperText={description}
                                    onBlur={() => {
                                        form.setFieldTouched(name);
                                    }}
                                    enumRange={enumRange}
                                    required={isRequired}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.CASCADING_LIST: {
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <CascadingList
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    cascadingListItems={attribute?.constraint?.cascadingListItems}
                                    displayName={attribute?.displayName}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        }
        default:
            return null;
    }
};
