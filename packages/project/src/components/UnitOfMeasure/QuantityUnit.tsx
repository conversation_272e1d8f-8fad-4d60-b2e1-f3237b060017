/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect, useCallback } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import CircularProgress from '@mui/material/CircularProgress';
import FormHelperText from '@mui/material/FormHelperText';
import { useUnitOfMeasure } from '@glidesystems/caching-store';
import { Paper, styled } from '@mui/material';

const StyledPaper = styled(Paper)(({ theme }) => ({
    borderRadius: 0,
    background: '#F7F8FC',
    fontSize: '14px',
    color: '#777777',
    boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.22)',
    '& .MuiListSubheader-root': {
        background: '#F7F8FC',
        fontWeight: 600,
    },
}));

export default function QuantityUnit(props) {
    const { onChange, onClose, value, groupId, metaData, size } = props;

    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);
    const [isFetched, setIsFetched] = useState(false);
    const [isDataFetching, setIsDataFetching] = useState(false);
    const [quantityUnitOption, setQuantityUnitOption] = useState([]);
    const { quantityUnit, getQuantityUnit } = useUnitOfMeasure((state) => state);

    const getQuantityUnits = useCallback(async () => {
        setIsDataFetching(true);
        await getQuantityUnit(groupId);
        setIsDataFetching(false);
    }, []);

    useEffect(() => {
        if (groupId) {
            if (quantityUnit[groupId]) {
                setIsFetched(true);
                return;
            }
            getQuantityUnits();
        }
    }, [groupId, quantityUnit]);

    useEffect(() => {
        const quantityUnitData = get(quantityUnit, groupId, null);
        if (value) {
            const matched = get(quantityUnitData, value, null);

            if (matched) {
                setSelected(matched);
            }
        }

        if (!isEmpty(quantityUnitData)) {
            setQuantityUnitOption(Object.values(quantityUnitData));
        }
    }, [isFetched, value, groupId, quantityUnit]);

    return (
        <>
            <Autocomplete
                open={open}
                onOpen={() => {
                    setOpen(true);
                }}
                PaperComponent={StyledPaper}
                onClose={() => {
                    setOpen(false);
                    onClose();
                }}
                disabled={isDataFetching || !groupId}
                value={selected}
                getOptionLabel={(option: any) => option?.label ?? option}
                isOptionEqualToValue={(option: any, v) => {
                    if (v.uri) {
                        return option.uri === v.uri;
                    }
                    return option.uri === v;
                }}
                options={quantityUnitOption}
                loading={isDataFetching}
                fullWidth
                renderInput={(params) => (
                    <TextField
                        {...params}
                        size={size}
                        label="Unit"
                        sx={{
                            input: {
                                paddingLeft: '11px!important',
                            },
                        }}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <React.Fragment>
                                    {isDataFetching ? <CircularProgress color="inherit" size={20} /> : null}
                                    {params.InputProps.endAdornment}
                                </React.Fragment>
                            ),
                        }}
                    />
                )}
                onChange={(event: any, newValue: any) => {
                    setSelected(newValue);
                    onChange(newValue?.uri || '');
                }}
                sx={{
                    '& label': { background: '#FFFFFF' },
                    '.Mui-disabled': {
                        '.MuiCircularProgress-root': {
                            color: '#1976d2',
                        },
                    },
                    '.MuiAutocomplete-inputRoot': {
                        borderRadius: '2px!important',
                    },
                }}
            />
            {metaData.touched && metaData.error && !value && (
                <FormHelperText className="Mui-error" sx={{ margin: '3px 14px 0 14px' }}>
                    {metaData.error}
                </FormHelperText>
            )}
        </>
    );
}
