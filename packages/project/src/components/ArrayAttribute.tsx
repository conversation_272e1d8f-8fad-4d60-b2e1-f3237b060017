/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useState } from 'react';
import { DatePicker, DateTimePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { CalendarMonth } from '@mui/icons-material';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { Autocomplete, Box, Chip, IconButton, SxProps, TextField } from '@mui/material';
import {
    formatDate,
    formatDateTime,
    formatSystemDate,
    formatSystemDateTime,
    MainTooltip,
} from '@glidesystems/styleguide';
import isEmpty from 'lodash/isEmpty';
import { AttributeType } from '@glidesystems/api';
import { useGlobalConfig } from '@glidesystems/caching-store';

export interface ArrayInputProps<T = string | number> {
    value: T[];
    onChange: (value: T[]) => void;
    displayName: string;
    size?: 'small' | 'medium';
    onBlur?: () => void;
    error?: string;
    helperText?: string;
    fullWidth?: boolean;
    type?: AttributeType.STRING_ARRAY | AttributeType.FLOAT_ARRAY | AttributeType.INTEGER_ARRAY;
    required?: boolean;
    sx?: SxProps;
}

export interface StringArrayInputProps extends ArrayInputProps<string> {
    enumRange?: string[];
}
export interface DateArrayInputProps extends ArrayInputProps<string> {
    isDateTime?: boolean;
    wrapperSx?: SxProps;
    anchorEl?: React.MutableRefObject<any>;
}

export const MultiValueInput = <T extends string | number>({
    value,
    onChange,
    displayName,
    size = 'small',
    error = '',
    helperText = '',
    onBlur = () => {},
    enumRange,
    required = false,
    fullWidth = true,
    type = AttributeType.STRING_ARRAY,
    sx = {},
    inputSx = {},
}: ArrayInputProps<T> & { enumRange?: T[]; inputSx?: SxProps }) => {
    const isFloat = type === AttributeType.FLOAT_ARRAY;
    const isInteger = type === AttributeType.INTEGER_ARRAY;
    const isNumberInput = isFloat || isInteger;
    const isEnum = !isEmpty(enumRange);

    const parseValue = (input: string): T | null => {
        if (isNumberInput) {
            const num = isFloat ? parseFloat(input) : parseInt(input, 10);
            return isNaN(num) ? null : (num as T);
        }
        return input as T;
    };

    const onAdd = (newValue: T | null) => {
        if (newValue !== null) {
            onChange([...(value || []), newValue]);
        }
    };

    const onRemove = (index: number) => {
        onChange(value?.filter((_, i) => i !== index));
    };

    return (
        <MainTooltip title="Semicolon (;) and Comma (,) are not supported in the list values">
            <span>
                <Autocomplete
                    multiple
                    freeSolo={!isEnum}
                    options={(isEnum ? enumRange : []) as T[]}
                    value={value || []}
                    size={size}
                    sx={sx}
                    onBlur={onBlur || (() => {})}
                    fullWidth={fullWidth}
                    renderTags={(selected, getTagProps) =>
                        selected.map((selectedValue, index) => (
                            <Chip
                                key={`item-${index}`}
                                label={selectedValue}
                                {...getTagProps({ index })}
                                onDelete={() => onRemove(index)}
                            />
                        ))
                    }
                    onChange={(_, newValue) => {
                        const parsedValues = newValue
                            .map((v) => parseValue(v as string))
                            .filter((v): v is T => v !== null);
                        onChange(parsedValues);
                    }}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label={displayName}
                            size={size}
                            variant="outlined"
                            InputLabelProps={{ shrink: true, required }}
                            error={Boolean(error)}
                            helperText={error ?? helperText}
                            inputProps={{
                                ...params.inputProps,
                                inputMode: isNumberInput ? 'decimal' : undefined,
                                pattern: isFloat ? '[0-9]*\\.?[0-9]*' : '[0-9]*',
                            }}
                            sx={inputSx}
                            onKeyDown={(event) => {
                                if (isEnum) return;
                                const { key } = event;
                                if (isNumberInput) {
                                    const isValidNumberKey =
                                        /[0-9]/.test(key) ||
                                        key === 'Backspace' ||
                                        key === 'Enter' ||
                                        key === 'Tab' ||
                                        key === 'ArrowLeft' ||
                                        key === 'ArrowRight';
                                    const inputValue = String(params.inputProps.value);
                                    const isDecimalPoint = key === '.' && isFloat && !inputValue.includes('.');
                                    if (!isValidNumberKey && !isDecimalPoint) {
                                        event.preventDefault();
                                    }
                                }

                                if (event.key === ';' || event.key === ',' || event.key === 'Enter') {
                                    event.preventDefault();
                                    event.stopPropagation();

                                    const newValue: any = params.inputProps.value;
                                    const parsedValue = parseValue(newValue);
                                    if (parsedValue !== null) {
                                        if (typeof parsedValue === 'string' && parsedValue.length === 0) {
                                            return;
                                        }
                                        onAdd(parsedValue);
                                        params.inputProps.onChange({
                                            target: { value: '' },
                                        } as any);
                                    }
                                }
                            }}
                        />
                    )}
                />
            </span>
        </MainTooltip>
    );
};

export const DateArrayInput = ({
    value,
    onChange,
    displayName,
    size = 'small',
    error = '',
    helperText = '',
    onBlur,
    isDateTime = false,
    required,
    fullWidth = true,
    sx = {},
    wrapperSx = {},
    anchorEl,
}: DateArrayInputProps) => {
    const [open, setOpen] = useState(false);
    const textFieldRef = useRef(null);
    const selectedDates = value || [];
    const handleAddDate = (newDate) => {
        if (newDate) {
            onChange([...selectedDates, isDateTime ? formatSystemDateTime(newDate) : formatSystemDate(newDate)]);
        }
    };

    const handleRemoveDate = (index) => {
        onChange(selectedDates?.filter((_, i) => i !== index));
    };

    const openDatePicker = (e) => {
        e.stopPropagation();
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
    return (
        <Box sx={wrapperSx} className="ag-custom-component-popup" onClick={(e) => e.stopPropagation()}>
            <LocalizationProvider dateAdapter={AdapterMoment}>
                <Autocomplete
                    multiple
                    sx={sx}
                    freeSolo
                    options={[]}
                    value={selectedDates}
                    size={size}
                    fullWidth={fullWidth}
                    renderTags={(selected, getTagProps) =>
                        selected.map((date, index) => (
                            <Chip
                                key={`date-${index}`}
                                label={isDateTime ? formatDateTime(date) : formatDate(date)}
                                {...getTagProps({ index })}
                                onDelete={() => handleRemoveDate(index)}
                            />
                        ))
                    }
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label={displayName}
                            size={size}
                            onClick={openDatePicker}
                            variant="outlined"
                            InputLabelProps={{ shrink: true, required }}
                            inputRef={textFieldRef}
                            sx={{
                                '& .MuiInputBase-root': {
                                    paddingRight: '8px !important',
                                },
                                backgroundColor: 'white',
                            }}
                            error={Boolean(error)}
                            helperText={error ?? helperText}
                            InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                    <>
                                        <IconButton size="small" onClick={openDatePicker}>
                                            <CalendarMonth />
                                        </IconButton>
                                    </>
                                ),
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                        />
                    )}
                />
                {isDateTime ? (
                    <DateTimePicker
                        label={displayName}
                        open={open}
                        value={new Date()}
                        onChange={() => {}}
                        onAccept={(newValue) => {
                            handleAddDate(newValue);
                        }}
                        inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                        onClose={handleClose}
                        PopperProps={{
                            anchorEl: anchorEl?.current || textFieldRef.current,
                            // Note: Below 2 props are needed for the DatePicker to render in ag-grid cell editor
                            disablePortal: Boolean(anchorEl),
                            keepMounted: true,
                            sx: {
                                backgroundColor: 'white',
                            },
                        }}
                        disabled={false}
                        renderInput={(params) => <TextField {...params} style={{ display: 'none' }} />}
                    />
                ) : (
                    <DatePicker
                        label={displayName}
                        open={open}
                        value={new Date()}
                        onChange={(newValue) => {
                            handleAddDate(newValue);
                        }}
                        onClose={handleClose}
                        inputFormat={useGlobalConfig.getState().getDateFormat()}
                        PopperProps={{
                            anchorEl: anchorEl?.current || textFieldRef.current,
                            // Note: Below two props are needed for the DatePicker to render in ag-grid cell editor
                            disablePortal: Boolean(anchorEl),
                            keepMounted: true,
                            sx: {
                                backgroundColor: 'white',
                            },
                        }}
                        disabled={false}
                        renderInput={(params) => <TextField {...params} style={{ display: 'none' }} />}
                    />
                )}
            </LocalizationProvider>
        </Box>
    );
};
