/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Avatar, Box, Tooltip, Typography } from '@mui/material';
import AssigneeAvatar from './AssigneeAvatar';

const MAX_AVATARS = 16;

export default function AssigneeRenderer({ value, fullHeight = false }: { value: any[]; fullHeight?: boolean }) {
    const assignees = value ?? [];
    const visibleAssignees = assignees.slice(0, MAX_AVATARS);
    const hiddenAssignees = assignees.slice(MAX_AVATARS);

    return (
        <Box
            sx={{
                '& .MuiAvatar-circular': { fontSize: '12px' },
                height: fullHeight ? '100%' : 'auto',
                display: 'flex',
                alignItems: 'center',
            }}
        >
            {visibleAssignees.length > 0 ? (
                <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                    {visibleAssignees.map((assignee, index) => (
                        <Box
                            key={assignee.id}
                            sx={{
                                ml: index === 0 ? 0 : -0.7,
                                zIndex: assignees.length - index,
                            }}
                        >
                            <AssigneeAvatar name={assignee.name} email={assignee.email} />
                        </Box>
                    ))}
                    {hiddenAssignees.length > 0 && (
                        <Tooltip
                            slotProps={{
                                tooltip: {
                                    sx: {
                                        background: '#FFFFFF',
                                        color: '#222222',
                                        boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.26)',
                                    },
                                },
                            }}
                            placement="top"
                            title={
                                <Box
                                    sx={{
                                        padding: '8px',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '4px',
                                        maxHeight: '360px',
                                        overflowY: 'auto',
                                    }}
                                >
                                    {hiddenAssignees.map((assignee) => (
                                        <Box
                                            key={assignee.id}
                                            sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}
                                        >
                                            <AssigneeAvatar name={assignee.name} email={assignee.email} />
                                            <Typography
                                                sx={{
                                                    fontSize: '14px',
                                                    fontWeight: 400,
                                                    lineHeight: '16px',
                                                    display: 'flex',
                                                    gap: '8px',
                                                    alignItems: 'center',
                                                    padding: '4px 8px',
                                                }}
                                            >
                                                {assignee.name}
                                            </Typography>
                                        </Box>
                                    ))}
                                </Box>
                            }
                        >
                            <Box sx={{ ml: visibleAssignees.length ? -0.7 : 0 }}>
                                <Avatar
                                    sx={{
                                        width: 26,
                                        height: 26,
                                        bgcolor: '#C5CAD7',
                                        color: '#153E78',
                                        fontSize: 12,
                                        border: '1px solid white',
                                    }}
                                >
                                    {`+${hiddenAssignees.length}`}
                                </Avatar>
                            </Box>
                        </Tooltip>
                    )}
                </Box>
            ) : (
                <Typography sx={{ fontSize: '14px', color: '#898282' }}>No one is assigned to this project</Typography>
            )}
        </Box>
    );
}
