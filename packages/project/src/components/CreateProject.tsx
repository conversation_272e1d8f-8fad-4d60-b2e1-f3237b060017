/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Autocomplete, Box, Button, FormHelperText, Grid, IconButton, TextField, Typography } from '@mui/material';
import {
    EntityDetail,
    Method,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
    fetch as apiFetch,
    batchRequestBody,
    entityUrls,
} from '@glidesystems/api';
import { useSchemaDetail, useRoles, useAuth } from '@glidesystems/caching-store';
import {
    AgentDropdown,
    buildValidationSchema,
    CloseIcon,
    DRAWER_COMPONENT_NAME,
    Loading,
    LoadingOverlay,
    notifyError,
    notifySuccess,
    ResizableDrawer,
    PlusIcon,
    DeleteIcon,
    MainTooltip,
} from '@glidesystems/styleguide';
import { Form, Formik } from 'formik';
import { useEffect, useMemo, useRef, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import AttributeBuilder from './AttributeBuilder';
import * as Yup from 'yup';

// Define the form values interface
interface FormValues {
    [key: string]: any;
    assignees?: Array<{
        assignee: EntityDetail;
        permissionRole: string | null;
    }>;
}

const CreateProject = ({ onRefresh }) => {
    const userInfo = useAuth((state) => state.userInfo);
    const [assignees, setAssignees] = useState([]);
    const [open, setOpen] = useState(false);
    const handleClose = () => {
        setOpen(false);
        setAssignees([]);
    };
    const handleOpen = () => setOpen(true);
    const formRef = useRef(null);
    const [error, setError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    // Fetch permission roles for the Autocomplete
    const permissionRoles = useRoles((state) => state.permissionRoles?.map((role) => role.name)) || [];
    const getPermissionRoles = useRoles((state) => state.getPermissionRoles);
    useEffect(() => {
        getPermissionRoles?.(true);
    }, [getPermissionRoles]);
    const [projectSchema, getSchema] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.PROJECT],
        state.getSchema,
    ]);
    const userRoles = userInfo?.roles?.map((r) => r.toLowerCase());
    const canCreate =
        userInfo?.isAdmin ||
        userInfo?.isSuperAdmin ||
        projectSchema?.accessibleOrgRoles?.length === 0 ||
        projectSchema?.accessibleOrgRoles?.some((role) => userRoles.includes(role.toLowerCase()));
    useEffect(() => {
        getSchema(SYSTEM_ENTITY_TYPE.PROJECT, true).then((schema) => {
            if (!schema) {
                setError(true);
            }
        });
    }, []);
    const [initialValues, validationSchema] = useMemo(() => {
        if (!projectSchema) {
            return [{} as FormValues, null];
        }
        const {
            attributes,
            entityType: { attributeOrder },
        } = projectSchema;
        const allAttributes = Object.values(attributes)
            .filter((attr) => attr.visible && isEmpty(attr.identifier) && attr.mutable)
            .sort((a, b) => {
                if (!attributeOrder) return a.name.localeCompare(b.name);
                return attributeOrder.indexOf(a.name) - attributeOrder.indexOf(b.name);
            });

        // Combine existing attribute validation with assignees validation
        const assigneesValidation = Yup.object({
            assignees: Yup.array()
                .of(
                    Yup.object({
                        assignee: Yup.object().nullable().required('Assignee is required'),
                        permissionRole: Yup.string().nullable().required('Permission role is required'),
                    })
                )
                .min(1, 'At least one assignee is required'),
        });
        const validationSchema = buildValidationSchema(allAttributes).concat(assigneesValidation);

        const initialValues: FormValues = Object.fromEntries(
            allAttributes.map((attribute) => {
                if (get(attribute, ['constraint', 'enumRange'], false)) {
                    return [attribute.name, attribute.defaultValue || null];
                }
                return [attribute.name, attribute.defaultValue || null];
            })
        );

        return [initialValues, validationSchema];
    }, [projectSchema]);

    const handleSubmit = async (values) => {
        try {
            setIsLoading(true);
            // Separate attribute values from assignees for entity creation
            const { assignees: assigneeRows = [], ...attributeValues } = values;
            const createdResponse = await apiFetch({
                ...entityUrls.createEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.PROJECT,
                },
                data: {
                    attributes: attributeValues,
                },
            });

            if (assigneeRows.length > 0) {
                const params = assigneeRows.map(({ assignee, permissionRole }) => ({
                    subParams: {
                        fromEntityId: createdResponse.data.id,
                        relationType: SYSTEM_RELATION.ACCESSOR,
                        toEntityId: assignee.id,
                    },
                    body: {
                        properties: {
                            permissionRole,
                        },
                    },
                }));
                const batchRequest = batchRequestBody(
                    Method.POST,
                    `/entity/:fromEntityId/:relationType/:toEntityId`,
                    params
                );
                const { data } = await apiFetch({
                    ...entityUrls.batchRequest,
                    data: batchRequest,
                });
                if (data.some((res) => !res.success || res.status != 200)) {
                    notifyError(`An error occurred while adding agents to the created project`);
                    return;
                }
            }
            onRefresh();
            notifySuccess(`Successfully created new project`);
            handleClose();
        } catch (err) {
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <>
            {isLoading && <LoadingOverlay />}
            <MainTooltip title={canCreate ? '' : 'You do not have permission to create a project'}>
                <span>
                    <Button
                        onClick={handleOpen}
                        variant="contained"
                        size="small"
                        disabled={!canCreate}
                        className="createBtn"
                        sx={{ padding: '0 24px' }}
                    >
                        Create Project
                    </Button>
                </span>
            </MainTooltip>
            <ResizableDrawer
                open={open}
                onClose={handleClose}
                componentName={DRAWER_COMPONENT_NAME.CREATE_PROJECT}
                defaultWidth={650}
                minWidth={475}
                disableCloseOnClickOutside
                disableEnforceFocus
            >
                <Box
                    sx={{
                        display: 'flex',
                        color: '#545454',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: '24px',
                        backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            gap: '16px',
                            position: 'relative',
                        }}
                    >
                        <Typography
                            sx={{
                                fontSize: '20px',
                                lineHeight: '150%',
                                fontWeight: 600,
                                cursor: 'default',
                                color: (theme) => theme.palette.glide.text.white,
                            }}
                        >
                            Create Project
                        </Typography>
                        <IconButton
                            onClick={handleClose}
                            sx={{
                                height: '24px',
                                width: '24px',
                                p: 0,
                                color: (theme) => theme.palette.glide.text.white,
                                marginLeft: 'auto',
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        overflow: 'hidden',
                    }}
                >
                    {projectSchema ? (
                        <>
                            <Box sx={{ flexGrow: 1, p: '24px', overflowY: 'auto', pt: '8px' }}>
                                <Formik<FormValues>
                                    enableReinitialize
                                    initialValues={initialValues}
                                    validationSchema={validationSchema}
                                    innerRef={formRef}
                                    onSubmit={(values) => {
                                        handleSubmit(values);
                                    }}
                                >
                                    {({ values, errors, setFieldValue, setFieldTouched, ...rest }) => (
                                        <Form id="create-project" {...rest}>
                                            <Grid container spacing="12px">
                                                <Grid item xs={12}>
                                                    <AttributeBuilder
                                                        setFieldTouched={setFieldTouched}
                                                        schema={projectSchema}
                                                        setFieldValue={setFieldValue}
                                                        errors={errors}
                                                        values={values}
                                                        label="General Information"
                                                    />
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <Typography
                                                        sx={{
                                                            fontSize: '14px',
                                                            fontWeight: 500,
                                                            color: (theme) => theme.palette.info.main,
                                                            mb: 2,
                                                        }}
                                                    >
                                                        Assignees
                                                    </Typography>
                                                    {(!values.assignees || values.assignees.length === 0) && (
                                                        <IconButton
                                                            size="small"
                                                            color="primary"
                                                            onClick={() =>
                                                                setFieldValue('assignees', [
                                                                    ...(values.assignees || []),
                                                                    { assignee: null, permissionRole: null },
                                                                ])
                                                            }
                                                        >
                                                            <PlusIcon />
                                                        </IconButton>
                                                    )}
                                                    {values.assignees?.map((assignee, index) => (
                                                        <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                                                            <Grid item xs={5}>
                                                                <AgentDropdown
                                                                    onBlur={() =>
                                                                        setFieldTouched(
                                                                            `assignees.${index}.assignee`,
                                                                            true
                                                                        )
                                                                    }
                                                                    value={assignee.assignee}
                                                                    onChange={(newValue) => {
                                                                        setFieldValue(
                                                                            `assignees.${index}.assignee`,
                                                                            newValue,
                                                                            true
                                                                        );
                                                                        setFieldTouched(
                                                                            `assignees.${index}.assignee`,
                                                                            true
                                                                        );
                                                                    }}
                                                                    error={Boolean(
                                                                        typeof errors.assignees?.[index] !== 'string' &&
                                                                            errors.assignees?.[index]?.assignee &&
                                                                            rest.touched?.assignees?.[index]?.assignee
                                                                    )}
                                                                    helperText={
                                                                        typeof errors.assignees?.[index] !== 'string' &&
                                                                        errors.assignees?.[index]?.assignee &&
                                                                        rest.touched?.assignees?.[index]?.assignee
                                                                            ? (errors.assignees[index]
                                                                                  .assignee as string)
                                                                            : ''
                                                                    }
                                                                />
                                                            </Grid>
                                                            <Grid item xs={5}>
                                                                <Autocomplete
                                                                    options={permissionRoles}
                                                                    value={assignee.permissionRole}
                                                                    onChange={(event, newValue) => {
                                                                        setFieldValue(
                                                                            `assignees.${index}.permissionRole`,
                                                                            newValue
                                                                        );
                                                                        setFieldTouched(
                                                                            `assignees.${index}.permissionRole`,
                                                                            true
                                                                        );
                                                                    }}
                                                                    onBlur={() =>
                                                                        setFieldTouched(
                                                                            `assignees.${index}.permissionRole`,
                                                                            true
                                                                        )
                                                                    }
                                                                    size="small"
                                                                    renderInput={(params) => (
                                                                        <TextField
                                                                            {...params}
                                                                            label="Permission Role"
                                                                            size="small"
                                                                            error={
                                                                                typeof errors.assignees?.[index] !==
                                                                                    'string' &&
                                                                                errors.assignees?.[index]
                                                                                    ?.permissionRole &&
                                                                                rest.touched?.assignees?.[index]
                                                                                    ?.permissionRole
                                                                            }
                                                                            helperText={
                                                                                typeof errors.assignees?.[index] !==
                                                                                    'string' &&
                                                                                errors.assignees?.[index]
                                                                                    ?.permissionRole &&
                                                                                rest.touched?.assignees?.[index]
                                                                                    ?.permissionRole
                                                                                    ? errors.assignees[index]
                                                                                          .permissionRole
                                                                                    : ''
                                                                            }
                                                                        />
                                                                    )}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={2} sx={{ display: 'flex', gap: 1 }}>
                                                                {index === values.assignees.length - 1 && (
                                                                    <IconButton
                                                                        onClick={() => {
                                                                            const newAssignees = [
                                                                                ...values.assignees,
                                                                                {
                                                                                    assignee: null,
                                                                                    permissionRole: null,
                                                                                },
                                                                            ];
                                                                            setFieldValue('assignees', newAssignees);
                                                                        }}
                                                                        size="small"
                                                                        sx={{ color: 'primary.main' }}
                                                                    >
                                                                        <PlusIcon />
                                                                    </IconButton>
                                                                )}
                                                                {values.assignees.length > 1 && (
                                                                    <IconButton
                                                                        onClick={() => {
                                                                            const newAssignees =
                                                                                values.assignees.filter(
                                                                                    (_, i) => i !== index
                                                                                );
                                                                            setFieldValue('assignees', newAssignees);
                                                                        }}
                                                                        size="small"
                                                                        sx={{ color: 'error.main' }}
                                                                    >
                                                                        <DeleteIcon />
                                                                    </IconButton>
                                                                )}
                                                            </Grid>
                                                        </Grid>
                                                    ))}
                                                    {errors.assignees && typeof errors.assignees === 'string' && (
                                                        <FormHelperText error sx={{ mt: 1 }}>
                                                            {errors.assignees}
                                                        </FormHelperText>
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </Form>
                                    )}
                                </Formik>
                            </Box>
                            <Box
                                sx={{
                                    marginTop: 'auto',
                                    p: '24px',
                                    display: 'flex',
                                    width: '100%',
                                    justifyContent: 'flex-end',
                                    gap: '8px',
                                }}
                            >
                                <Button
                                    sx={{
                                        width: { xs: '120px', msFlexDirection: '160px' },
                                        justifyContent: 'flex-start',
                                    }}
                                    variant="contained"
                                    color="secondary"
                                    size="medium"
                                    onClick={handleClose}
                                >
                                    Cancel
                                </Button>
                                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-start' }}>
                                    <Button
                                        onClick={() => formRef.current.submitForm()}
                                        className="actionBtn"
                                        variant="contained"
                                        color="primary"
                                        size="medium"
                                        sx={{ minWidth: '180px', justifyContent: 'flex-start' }}
                                    >
                                        Create
                                    </Button>
                                </Box>
                            </Box>
                        </>
                    ) : (
                        <Loading />
                    )}
                </Box>
            </ResizableDrawer>
        </>
    );
};

export default CreateProject;
