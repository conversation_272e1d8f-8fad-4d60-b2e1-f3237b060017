.project-variables:
  variables:
    PACKAGE_DIR: packages/project

aws-stag-project-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .project-variables

aws-stag-project-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .project-variables
  needs:
    - aws-stag-project-package

gcp-stag-project-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .project-variables

gcp-stag-project-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .project-variables
  needs:
    - gcp-stag-project-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-project-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .project-variables

gcp-uat-project-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .project-variables
  needs:
    - gcp-uat-project-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json