.catalog-variables:
  variables:
    PACKAGE_DIR: packages/catalog

aws-stag-catalog-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .catalog-variables

aws-stag-catalog-publish:
  extends: 
    - .aws-bucket-publish
    - .aws-stag-variables
    - .catalog-variables
  needs:
    - aws-stag-catalog-package

gcp-stag-catalog-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .catalog-variables

gcp-stag-catalog-publish:
  extends: 
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .catalog-variables
  needs:
    - gcp-stag-catalog-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-catalog-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .catalog-variables

gcp-uat-catalog-publish:
  extends: 
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .catalog-variables
  needs:
    - gcp-uat-catalog-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json