/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch as apiFetch, classificationUrls } from '@glidesystems/api';
import { Classification, ClassificationSchema } from '../model/classification';

type CatalogState = {
    classifications: Classification[];
    classificationSchema: Record<string, ClassificationSchema>;
    showBrowser: boolean;
    flattenClassifications: any;
    isLoading: boolean;
    isLoadingClassificationSchema: boolean;
    isLoadingRefreshClassifications: boolean;
    isLoadingApplyingClassifications: boolean;
    onFetchClassifications: (isRefresh?: boolean) => void;
    getClassificationSchema: (name: string) => void;
    setIsLoadingApplyingClassifications: (state: boolean) => void;
    updateFlattenClassificationTree: (value: any) => void;
    setShowBrowser: (value: boolean) => void;
};

export const flattenTree = (node, path: any[] = [], treeMap = {}) => {
    const { name } = node;
    const newPath = [...path, name];
    const children = node.subTypes || [];
    treeMap[name] = { name, data: node, path: newPath, isLeaf: children.length === 0, value: false };
    children.forEach((child) => {
        flattenTree(child, newPath, treeMap);
    });
    return treeMap;
};
export const useStore = create<CatalogState>((set, get) => ({
    // States
    showBrowser: true,
    classifications: [],
    classificationSchema: {},
    isLoading: false,
    isLoadingClassificationSchema: false,
    isLoadingRefreshClassifications: false,
    isLoadingApplyingClassifications: false,
    flattenClassifications: {},
    // Actions
    onFetchClassifications: async (isRefresh = false) => {
        try {
            if (isRefresh) {
                set({ isLoadingRefreshClassifications: true });
            } else {
                set({ isLoading: true });
            }
            const { data } = await apiFetch({
                ...classificationUrls.getClassificationTree,
                qs: {
                    show_number_entity: true,
                },
            });

            let treeMap = {};
            data.forEach((classification) => {
                flattenTree(classification, [], treeMap);
            });
            set({ classifications: data, flattenClassifications: treeMap });
        } catch (e) {
        } finally {
            if (isRefresh) {
                set({ isLoadingRefreshClassifications: false });
            } else {
                set({ isLoading: false });
            }
        }
    },
    getClassificationSchema: async (name) => {
        const classificationSchema = get().classificationSchema;

        if (classificationSchema[name]) return;

        try {
            set({ isLoadingClassificationSchema: true });
            const data = await apiFetch({
                ...classificationUrls.getClassificationDetail,
                params: { name },
            });
            set({
                isLoadingClassificationSchema: false,
                classificationSchema: { ...classificationSchema, [name]: data.data },
            });
        } catch (e) {
            set({ isLoadingClassificationSchema: false });
        }
    },
    setIsLoadingApplyingClassifications: (state) => {
        set({ isLoadingApplyingClassifications: state });
    },
    updateFlattenClassificationTree: (newValue) => {
        set({ flattenClassifications: newValue });
    },
    setShowBrowser: (value) => {
        set({ showBrowser: value });
    },
}));
