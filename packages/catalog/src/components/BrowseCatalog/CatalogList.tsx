/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import {
    Loading,
    filterParams,
    buildQueryBasedOnFilter,
    buildSortParams,
    commonMessages,
    notifyError,
    notifySuccess,
    CloseIcon,
    checkingPermission,
    tableIcons,
    Thumbnail,
    formatDateTime,
    PropertyValueRenderer,
    getFilterParamsByType,
} from '@glidesystems/styleguide';
import { useParams, useSearchParams } from 'react-router-dom';
import {
    fetch as apiFetch,
    AttributeType,
    batchRequestBody,
    entityUrls,
    getThumbnailUrl,
    Method,
} from '@glidesystems/api';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import StatusCellRenderer from '../Renderer/StatusCellRenderer';
import { Typography, Button, Grow, Switch, FormControlLabel, Box, CircularProgress } from '@mui/material';
import { useStore } from '../../store/store';
import CatalogNoRowsOverlay from '../NoRowsOverlay/CatalogNoRowsOverlay';
import { useAuth, useSchemaTree } from '@glidesystems/caching-store';
import ToolbarItem from '../ToolbarItem';
import { useDialogStore } from '../../store/dialogStore';
import AddEntity from '../AddEntity/AddEntity';
import { ModuleRegistry } from '@ag-grid-community/core';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';

import { LicenseManager } from '@ag-grid-enterprise/core';

LicenseManager.setLicenseKey(process.env.AG_GRID_LICENSE);

ModuleRegistry.registerModules([
    ServerSideRowModelModule,
    ColumnsToolPanelModule,
    FiltersToolPanelModule,
    MenuModule,
    ExcelExportModule,
    SetFilterModule,
]);

const getDynamicFilterType = (attribute) => {
    const { type } = attribute;
    const enumRange = get(attribute, ['constraint', 'enumRange'], []);
    if (!isEmpty(enumRange)) return 'agSetColumnFilter';

    if (type === AttributeType.DATE || type === AttributeType.DATE_TIME) {
        return 'agDateColumnFilter';
    }

    if ([AttributeType.INTEGER, AttributeType.LONG, AttributeType.FLOAT].includes(type)) {
        return 'agNumberColumnFilter';
    }

    return 'agTextColumnFilter';
};

const isEnum = (attribute) => !isEmpty(get(attribute, ['constraint', 'enumRange'], []));

const NameRenderer = ({ value, data }) => {
    const {
        id,
        properties: { name, type, hasThumbnail },
    } = data;
    const [searchParams, setSearchParams] = useSearchParams();
    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Thumbnail
                hasThumbnail={hasThumbnail}
                url={getThumbnailUrl(type, id)}
                alt={name}
                size={{ width: 24, height: 24 }}
            />
            <Typography
                sx={{
                    textDecoration: 'none',
                    color: (theme) => theme.palette.info.main,
                    fontSize: '14px',
                    fontWeight: 400,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    '&:hover': {
                        cursor: 'pointer',
                    },
                }}
                onClick={() => {
                    searchParams.set('entity_id', id);
                    searchParams.set('entity_type', type);
                    searchParams.set('entity_name', name);
                    setSearchParams(searchParams);
                }}
            >
                {value}
            </Typography>
        </Box>
    );
};

const getEnumFilterValues = (attribute) => {
    const enumRange = get(attribute, ['constraint', 'enumRange'], []);
    if (!isEmpty(enumRange)) {
        return enumRange;
    }
    return [];
};

const buildColumnDefs = (classification) => {
    let defaultColumns: any[] = [
        {
            field: 'properties.name',
            headerName: 'Name',
            cellRenderer: NameRenderer,
            flex: 1,
            filter: 'agTextColumnFilter',
            filterParams,
            checkboxSelection: true,
            minWidth: 220,
            showDisabledCheckboxes: true,
            pinned: 'left',
            headerTooltip: 'Entity Name',
        },
        {
            field: 'properties.type',
            headerName: 'Type',
            flex: 1,
            filter: 'agSetColumnFilter',
            filterParams: {
                ...filterParams,
                values: classification?.applicableEntityTypes,
            },
            minWidth: 120,
            headerTooltip: 'Entity Type',
            valueGetter: (props) => {
                if (props.context.schemaTreeMap) {
                    return (
                        props.context.schemaTreeMap?.[props.data?.properties?.type]?.displayName ||
                        props.data?.properties?.type
                    );
                }
                return props.data?.properties?.type;
            },
        },
        {
            field: 'state.name',
            headerName: 'Status',
            cellRenderer: StatusCellRenderer,
            flex: 1,
            filter: false,
            minWidth: 100,
            headerTooltip: 'Current state of the entity',
        },
        {
            field: 'lifecycle.name',
            headerName: 'Lifecycle',
            flex: 1,
            filter: false,
            minWidth: 100,
            filterParams,
            headerTooltip: 'Lifecycle',
        },
        {
            field: 'properties.description',
            headerName: 'Description',
            maxWidth: 250,
            flex: 1,
            filter: 'agTextColumnFilter',
            filterParams,
            valueFormatter: ({ value }) => {
                const plainText = value ? value.replace(/<\/?[^>]+>/gi, ' ') : value;
                return plainText;
            },
            cellRenderer: PropertyValueRenderer,
            headerTooltip: 'Description',
        },
    ];

    // Dynamic columns base on classification's attributes
    Object.values(get(classification, ['attributes'], {})).forEach((att: any) => {
        if (att.visible && !['description', 'name'].includes(att.name)) {
            defaultColumns.push({
                field: `properties.${att.name}`,
                headerName: att.displayName || att.name,
                flex: 1,
                filter: getDynamicFilterType(att),
                headerTooltip: att.description || att.displayName,
                filterParams: {
                    ...getFilterParamsByType(att.type),
                },
            });
        }
    });
    defaultColumns.concat([
        {
            field: 'properties.createdAt',
            headerName: 'Created At',
            hide: true,
            valueFormatter: (params) => {
                return formatDateTime(params.value);
            },
            editable: false,
            minWidth: 160,
            flex: 1,
        },
        {
            field: 'properties.updatedAt',
            headerName: 'Updated At',
            hide: true,
            valueFormatter: (params) => {
                return formatDateTime(params.value);
            },
            editable: false,
            minWidth: 160,
            flex: 1,
        },
    ]);
    return defaultColumns;
};

const CatalogList = () => {
    const { classification } = useParams();
    const {
        classificationSchema,
        onFetchClassifications,
        setIsLoadingApplyingClassifications,
        flattenClassifications,
        showBrowser,
        setShowBrowser,
        getClassificationSchema,
    } = useStore();
    const { schemaTreeMap } = useSchemaTree();

    const { onOpenDialog } = useDialogStore();
    const gridRef = useRef<AgGridReact>();
    const columnFilterRef = useRef(false);
    const [selectedRows, setSelectedRows] = useState([]);
    const userInfo = useAuth((state) => state.userInfo);
    const [pageInfo, setPageInfo] = useState(null);
    const [addEntity, setAddEntity] = useState(false);

    const schema = useMemo(
        () => classificationSchema[classification] || schema,
        [classification, classificationSchema]
    );

    const getContextMenuItems = useCallback(() => {
        return ['autoSizeAll', 'copy', 'export'];
    }, []);

    const getRowId = useCallback(function (params) {
        return params.data.id;
    }, []);

    const onFirstDataRendered = useCallback(() => {
        gridRef.current.columnApi.autoSizeColumn('properties.name');
    }, []);

    const columnDefs = useMemo(
        () => buildColumnDefs(flattenClassifications[classification]),
        [flattenClassifications, classification]
    );

    const gridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            floatingFiltersHeight: 34,
            columnDefs,
            loadingOverlayComponent: Loading,
            defaultColDef: {
                minWidth: 100,
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: columnFilterRef.current,
                autoHeight: true,
                wrapText: true,
            },
            getContextMenuItems: getContextMenuItems,
            noRowsOverlayComponent: CatalogNoRowsOverlay,
            noRowsOverlayComponentParams: {
                classification,
            },
            cacheBlockSize: 100,
            suppressColumnVirtualisation: true,
            serverSideInfiniteScroll: true,
            defaultExcelExportParams: {
                sheetName: 'Results',
                allColumns: true,
                fileName: `Catalog`,
                autoConvertFormulas: true,
                processCellCallback: (params) => {
                    const {
                        id,
                        properties: { type },
                        owner: { id: ownerId, name: ownerName, type: ownerType },
                    } = params.node.data;
                    const field = params.column.getColDef().field;

                    if (field === 'owner.name')
                        return `=HYPERLINK("${window.location.origin}/detail/${ownerType}/${ownerId}/properties", "${ownerName}")`;
                    else if (field === 'properties.name')
                        return `=HYPERLINK("${window.location.origin}/detail/${type}/${id}/properties", "${params.value}")`;
                    else return params.value;
                },
            },
            excelStyles: [
                {
                    id: 'hyperlinks',
                    font: {
                        color: '#3246D2',
                    },
                },
                {
                    id: 'Search-revisionTitle',
                    font: {
                        color: '#3246D2',
                    },
                },
            ],
            sideBar: {
                toolPanels: [
                    {
                        id: 'columns',
                        labelDefault: 'Columns',
                        labelKey: 'columns',
                        iconKey: 'columns',
                        toolPanel: 'agColumnsToolPanel',
                        toolPanelParams: {
                            suppressPivots: true,
                            suppressPivotMode: true,
                            suppressRowGroups: true,
                            suppressValues: true,
                        },
                    },
                    {
                        id: 'filters',
                        labelDefault: 'Filters',
                        labelKey: 'filters',
                        iconKey: 'filter',
                        toolPanel: 'agFiltersToolPanel',
                    },
                ],
            },
            icons: tableIcons,
            loadingCellRenderer: () => <CircularProgress style={{ marginLeft: '16px' }} size={16} color="info" />,
            onSelectionChanged: ({ api }) => {
                setSelectedRows(api.getSelectedRows());
            },
            isRowSelectable: ({ data }) => {
                const { permissions, properties } = data;
                const { canDelete } = permissions;
                const { isLocked } = properties;

                if (isLocked) {
                    return checkingPermission(data, userInfo, 'canDelete');
                }
                return canDelete;
            },
            rowSelection: 'multiple',
            getRowId,
            suppressRowClickSelection: true,
            rowStyle: {
                backgroundColor: '#FFFFFF',
                alignItems: 'center',
            },
            rowModelType: 'serverSide',
            onFirstDataRendered,
        }),
        [getRowId, userInfo]
    );

    const onDeselect = useCallback(() => {
        gridRef.current.api.deselectAll();
    }, []);

    const refreshServerSide = () => {
        onFetchClassifications(true);
        setSelectedRows([]);
        gridRef.current.api.refreshServerSide({ purge: false });
    };

    const handleRemoveEntities = async () => {
        setIsLoadingApplyingClassifications(true);
        try {
            const entityDetailUrls = selectedRows.map((row) => ({
                subParams: { entityType: row.properties.type, entityId: row.id },
            }));
            const batchEntityDetails = batchRequestBody(Method.GET, `/entity/:entityType/:entityId`, entityDetailUrls);
            const { data } = await apiFetch({
                ...entityUrls.batchRequest,
                data: batchEntityDetails,
            });
            if (data.some((res) => !res.success)) {
                notifyError(commonMessages.failedRemoveEntitiesClassification);
                return;
            }
            const updateReqBody = data.map((res) => {
                let newClassifications = res.response.classifications
                    .filter((c) => c.name !== classification)
                    .map((c) => c.name);
                return {
                    subParams: { entityId: res.response.id },
                    body: {
                        classifications: newClassifications,
                    },
                };
            });
            const updateBatchReq = batchRequestBody(Method.PUT, `/entity/:entityId`, updateReqBody);
            const { data: updateData } = await apiFetch({
                ...entityUrls.batchRequest,
                data: updateBatchReq,
            });
            if (updateData.some((res) => !res.success)) {
                notifyError(commonMessages.failedRemoveEntitiesClassification);
                return;
            }
            notifySuccess(
                <span>
                    Successfully removed selected entities on the <b>{classification}</b>
                </span>
            );
            refreshServerSide();
        } catch (error) {
            notifyError(commonMessages.failedRemoveEntitiesClassification);
        } finally {
            setIsLoadingApplyingClassifications(false);
        }
    };

    const onRemoveEntity = () => {
        if (selectedRows.length > 0) {
            onOpenDialog(
                'Remove classifications',
                `Do you really want to remove selected entities from the classification <b>${classification}</b>?`,
                handleRemoveEntities,
                'error'
            );
        }
    };

    const handleOpenAddEntity = () => {
        setAddEntity(true);
    };

    const toggleColumnFilters = useCallback(() => {
        let colDefs = gridRef.current.api.getColumnDefs();
        const enabled = !Boolean(colDefs.some((colDef: any) => colDef.floatingFilter));

        colDefs.forEach((colDef: any) => {
            colDef.floatingFilter = enabled;
        });
        columnFilterRef.current = enabled;
        gridRef.current.api.setColumnDefs(colDefs);

        gridRef.current.api.refreshHeader();
    }, []);

    const toggleBrowser = useCallback((e) => {
        setShowBrowser(e.target.checked);
    }, []);

    const validateRemovePermissions = () => {
        if (!flattenClassifications[classification]?.isLeaf) {
            return { message: 'Can remove entity from classification at leaf level only' };
        }
        if (selectedRows.length === 0) {
            return { message: 'Select an entity to remove' };
        }

        let noPermissionRows = [];
        selectedRows.forEach((row) => {
            const { isLocked } = row.properties;
            const { canClassify, canModify } = row.permissions;

            if (isLocked) {
                if (
                    !checkingPermission(row, userInfo, 'canModify') ||
                    !checkingPermission(row, userInfo, 'canClassify')
                ) {
                    noPermissionRows.push(row.properties.name);
                }
            } else if (!canClassify || !canModify) {
                noPermissionRows.push(row.properties.name);
            }
        });

        if (noPermissionRows.length > 0) {
            return {
                message: `You don't have permissions to classify below entities:`,
                details: noPermissionRows,
            };
        }
        return null;
    };
    const onCloseAddEntity = () => {
        setAddEntity(false);
    };

    const refreshFilters = () => {
        gridRef.current.api.setFilterModel(null);
        const setCols: any = gridRef.current.api.getFilterInstance('properties.type');
        setCols.setFilterValues(schema?.applicableEntityTypes);
        const dynamicAttributes = Object.values(get(schema, ['attributes'], {}));
        dynamicAttributes.forEach((attr: any) => {
            if (attr.visible) {
                const colFilter: any = gridRef.current.api.getFilterInstance(`properties.${attr.name}`);
                if (colFilter) {
                    if (isEnum(attr)) {
                        colFilter.setFilterValues(getEnumFilterValues(attr));
                    }
                }
            }
        });
    };

    const removePermissions = validateRemovePermissions();

    const addErrors = flattenClassifications[classification]?.isLeaf
        ? null
        : { message: 'Can add classification at leaf level only' };

    const handleChangeDataSource = () => {
        if (gridRef.current?.api && schema) {
            const datasource = getDataSource(classification, gridRef, setPageInfo);
            gridRef.current.api.setServerSideDatasource(datasource);

            gridRef.current.api.setColumnDefs(buildColumnDefs(schema));
            refreshFilters();
        }
    };

    useEffect(() => {
        getClassificationSchema(classification);
    }, [classification]);

    useEffect(() => {
        handleChangeDataSource();
    }, [schema]);

    return (
        <>
            <div className="toolbar">
                {classification && (
                    <div className="static">
                        <div className="catalogSummary">
                            <Typography className="title">{classification}</Typography>
                            <Typography className="pageInfo">{pageInfo?.total || 0} results</Typography>
                        </div>
                    </div>
                )}
                <div className="static">
                    <ToolbarItem title="Add Entity" onClick={handleOpenAddEntity} errors={addErrors} />
                    <ToolbarItem title="Remove Entity" onClick={onRemoveEntity} errors={removePermissions} />
                    <ToolbarItem title="Column Filter" onClick={toggleColumnFilters} />
                    <ToolbarItem title="Export Excel" onClick={() => gridRef.current.api.exportDataAsExcel()} />
                </div>
                <Grow in={!isEmpty(selectedRows)}>
                    <Button onClick={onDeselect} variant="ghost-blue" size="small" endIcon={<CloseIcon />}>
                        Deselect
                    </Button>
                </Grow>
                <FormControlLabel
                    className="switchLabel"
                    label="Browser"
                    control={<Switch size="small" onChange={toggleBrowser} defaultChecked={showBrowser} />}
                />
            </div>
            <div className="catalogTable">
                <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
                    <AgGridReact {...gridOptions} ref={gridRef} context={{ schemaTreeMap: schemaTreeMap }} />
                </div>
            </div>
            <AddEntity
                selectedClassification={classification}
                open={addEntity}
                onClose={onCloseAddEntity}
                refreshServerSide={refreshServerSide}
            />
        </>
    );
};

export default CatalogList;

const getDataSource = (classification: string, gridRef: React.MutableRefObject<AgGridReact>, setPageInfo) => {
    return {
        getRows: (params) => {
            if (!classification) {
                params.success({
                    rowData: [],
                    rowCount: 0,
                });
                gridRef.current.api.showNoRowsOverlay();
                return;
            }
            apiFetch({
                ...entityUrls.getListEntitySysRoot,
                qs: buildParams(params, classification),
            })
                .then((response) => {
                    const {
                        status,
                        data: { data, pageInfo },
                    } = response;
                    if (status !== 200) {
                        params.fail();
                        return;
                    }
                    setPageInfo(pageInfo);
                    params.success({
                        rowData: data,
                        rowCount: pageInfo.total || data.length,
                    });
                })
                .catch(() => {
                    params.fail();
                })
                .finally(() => {
                    gridRef.current.api.hideOverlay();
                    if (gridRef.current.api.getDisplayedRowCount() === 0) {
                        gridRef.current.api.showNoRowsOverlay();
                    }
                });
        },
    };
};

const buildParams = (params, selected: string) => {
    const filterModel = params.request.filterModel;
    const filterConditions = [];
    return {
        offset: params.request?.startRow || 0,
        limit: 100,
        classification: selected,
        fields: 'classifications',
        query: JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel)),
        ...buildSortParams(params.request.sortModel),
    };
};
