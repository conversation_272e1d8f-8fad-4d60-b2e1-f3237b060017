/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled } from '@mui/material';
import { tableStyles } from '@glidesystems/styleguide';

const Container = styled('div')(({ theme }) => ({
    marginTop: '56px',
    display: 'flex',
    width: '100%',
    height: 'calc(100vh - 56px)',
    '& .switchLabel': {
        fontSize: '14px',
        fontWeight: 400,
        color: theme.palette.glide.text.tertiary,
        gap: '8px',
        marginLeft: 'auto',
    },
    '& .pageTitle': {
        backgroundColor: theme.palette.glide.background.normal.tertiary,
        padding: '24px',
        border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        '& .title': {
            color: theme.palette.glide.text.normal.tertiary,
            fontWeight: 600,
            fontSize: '20px',
        },
    },
    '& .browsePanel': {
        width: 320,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: theme.palette.glide.background.normal.white,
        transition: 'width 0.2s',
        '& .tree': {
            flexGrow: 1,
            minHeight: 0,
            marginBottom: '16px',
        },
        '&.close': {
            width: 0,
        },
    },
    '& .catalogSummary': {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        '& .title': {
            fontSize: '18px',
            fontWeight: 600,
            color: theme.palette.glide.text.normal.inverseTertiary,
        },
        '& .pageInfo': {
            fontSize: '10px',
            color: theme.palette.glide.text.normal.inverseTertiary,
            fontWeight: 500,
        },
    },
    '& .content': {
        backgroundColor: theme.palette.glide.background.normal.white,
        height: '100%',
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        '& .toolbar': {
            padding: '8px 16px',
            marginBottom: 0,
            background: theme.palette.glide.background.normal.white,
            display: 'flex',
            gap: '4px',
            borderBottom: '1px solid #CED1D7',
            alignItems: 'center',
        },
        '& .static': {
            borderRight: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
            paddingRight: '24px',
            marginRight: '12px',
            display: 'flex',
            gap: '8px',
        },
        '& .catalogTable': {
            flexGrow: 1,
            marginBottom: '4px',
            marginTop: 0,
            ...tableStyles,
        },
    },
}));
export default Container;
