/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect } from 'react';
import { useDebounce, ResizableDrawer, SearchIcon, CloseIcon } from '@glidesystems/styleguide';
import { TextField, Button, InputAdornment, Box, IconButton, Typography } from '@mui/material';

const RightTray = ({
    title,
    open,
    onClose,
    onSearchChange = (search: string) => {},
    onConfirm,
    children,
    enableSearch = false,
    confirmText = 'Confirm',
    componentName,
    searchMsg = null,
    disableCloseOnClickOutside = false,
}) => {
    const [searchText, setSearchText] = useState(null);
    const debouncedSearchText = useDebounce(searchText, 400);

    const handleClose = () => {
        setSearchText(null);
        onClose();
    };

    useEffect(() => {
        onSearchChange(debouncedSearchText);
    }, [debouncedSearchText]);

    useEffect(() => {
        if (!open) {
            setSearchText(null);
        }
    }, [open]);

    const handleSearch = (e) => {
        setSearchText(e.target.value);
    };

    return (
        <ResizableDrawer
            open={open}
            onClose={handleClose}
            componentName={componentName}
            defaultWidth={475}
            minWidth={290}
            disableCloseOnClickOutside={disableCloseOnClickOutside}
        >
            <Box
                sx={{
                    display: 'flex',
                    color: (theme) => theme.palette.glide.text.normal.tertiary,
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: '24px',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        gap: '16px',
                        position: 'relative',
                    }}
                >
                    <Typography
                        sx={{
                            fontSize: '20px',
                            lineHeight: '150%',
                            fontWeight: 600,
                            cursor: 'default',
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                        }}
                    >
                        {title}
                    </Typography>
                    <IconButton
                        onClick={onClose}
                        sx={{
                            position: 'absolute',
                            right: 0,
                            height: '24px',
                            width: '24px',
                            p: 0,
                            color: (theme) => theme.palette.glide.text.white,
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Box>
            </Box>
            {enableSearch && (
                <Box sx={{ margin: '16px 24px' }}>
                    <TextField
                        sx={{
                            '& input': {
                                paddingLeft: '4px !important',
                                fontSize: '14px',
                                height: '24px',
                            },
                        }}
                        onChange={handleSearch}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    <SearchIcon />
                                </InputAdornment>
                            ),
                        }}
                        placeholder="Search something..."
                        fullWidth
                        variant="outlined"
                        size="medium"
                    />
                    {searchMsg && (
                        <Typography
                            sx={{
                                fontSize: '12px',
                                fontWeight: 400,
                                color: (theme) => theme.palette.glide.text.normal.inverseTertiary,
                                marginLeft: '8px',
                                marginTop: '4px',
                            }}
                        >
                            {searchMsg}
                        </Typography>
                    )}
                </Box>
            )}
            {children}
            <Box
                sx={{
                    margin: '16px 24px',
                    display: 'flex',
                    marginTop: 'auto',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    paddingTop: '16px',
                }}
            >
                <Button
                    sx={{
                        width: '160px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={onClose}
                >
                    Cancel
                </Button>
                <Button
                    sx={{
                        width: '100%',
                        justifyContent: 'flex-start',
                        maxWidth: '260px',
                    }}
                    variant="contained"
                    onClick={onConfirm}
                    color="info"
                >
                    {confirmText}
                </Button>
            </Box>
        </ResizableDrawer>
    );
};

export default RightTray;
