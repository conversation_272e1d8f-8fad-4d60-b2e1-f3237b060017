/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Box, Typography } from '@mui/material';
import { NoResultIcon } from '@glidesystems/styleguide';

const CatalogNoRowsOverlay = ({ classification = null }) => {
    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                p: 2,
                opacity: 0.8,
            }}
        >
            <NoResultIcon />
            {classification ? (
                <>
                    <Typography sx={{ mt: 2, mx: 3, fontSize: '16px' }}>
                        There are no entities classified by <b>{classification}</b>.
                    </Typography>
                    <Typography sx={{ fontSize: '16px' }}>
                        Try to select another classification or classify some entities.
                    </Typography>
                </>
            ) : (
                <Typography sx={{ fontSize: '16px' }}>
                    Select a <b>Classification</b> to browse
                </Typography>
            )}
        </Box>
    );
};
export default CatalogNoRowsOverlay;
