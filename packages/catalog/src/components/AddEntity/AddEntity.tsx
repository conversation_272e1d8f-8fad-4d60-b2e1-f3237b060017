/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import {
    Loading,
    filterParams,
    NoRowsOverlay,
    buildQueryBasedOnFilter,
    buildSortParams,
    DRAWER_COMPONENT_NAME,
    commonMessages,
    tableStyles,
    notifyError,
    notifySuccess,
    tableIcons,
} from '@glidesystems/styleguide';
import {
    fetch as apiFetch,
    entityUrls,
    buildAndOperatorQuery,
    buildContainsQuery,
    buildExactQuery,
    buildInQuery,
    buildOrOperatorQuery,
    batchRequestBody,
    Method,
} from '@glidesystems/api';
import { useStore } from '../../store/store';
import isNil from 'lodash/isNil';
import RightTray from '../RightTray/RightTray';
import { Box } from '@mui/material';
import { ModuleRegistry, GridOptions } from '@ag-grid-community/core';
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model';
import Grow from '@mui/material/Grow';
import { useSchemaTree } from '@glidesystems/caching-store';

ModuleRegistry.registerModules([InfiniteRowModelModule]);

const AddEntity = ({ open, onClose, refreshServerSide, selectedClassification }) => {
    const { isLoadingClassificationSchema, classificationSchema, setIsLoadingApplyingClassifications } = useStore();
    const gridRef = useRef<AgGridReact>();
    const applicableEntityTypes = useRef<string[]>([]);
    const selectedRef = useRef(classificationSchema[selectedClassification]?.classification?.name);
    const searchQuery = useRef(null);
    const [search, setSearch] = useState('');
    const { schemaTreeMap } = useSchemaTree();

    const handleClose = () => {
        searchQuery.current = null;
        setSearch('');
        onClose();
    };

    const handleSubmit = useCallback(async () => {
        const selectedRows = gridRef.current.api.getSelectedRows();
        setIsLoadingApplyingClassifications(true);
        if (selectedRows.length > 0) {
            try {
                const entityDetailUrls = selectedRows.map((row) => ({
                    subParams: { entityType: row.properties.type, entityId: row.id },
                }));
                const batchEntityDetails = batchRequestBody(
                    Method.GET,
                    `/entity/:entityType/:entityId`,
                    entityDetailUrls
                );
                const { data } = await apiFetch({
                    ...entityUrls.batchRequest,
                    data: batchEntityDetails,
                });
                if (data.some((res) => !res.success)) {
                    notifyError(commonMessages.failedApplyingClassification);
                    return;
                }
                const updateReqBody = data.map((res) => {
                    let appliedClassification = res.response.classifications || [];
                    return {
                        subParams: { entityType: res.response.properties.type, entityId: res.response.id },
                        body: {
                            classifications: Array.from(
                                new Set([...appliedClassification.map((c) => c.name), selectedRef.current])
                            ),
                        },
                    };
                });
                const updateBatchReq = batchRequestBody(Method.PUT, `/entity/:entityId`, updateReqBody);
                const { data: updateData } = await apiFetch({
                    ...entityUrls.batchRequest,
                    data: updateBatchReq,
                });
                if (updateData.some((res) => !res.success)) {
                    notifyError(commonMessages.failedApplyingClassification);
                    return;
                }
                notifySuccess(commonMessages.successfullyApplyingClassification);
                refreshServerSide();
                handleClose();
            } catch (error) {
                notifyError(commonMessages.failedApplyingClassification);
            } finally {
                setIsLoadingApplyingClassifications(false);
            }
        }
    }, []);
    const handleSearchChanged = (text) => {
        searchQuery.current = text;
        setSearch(text);
        if (gridRef.current) {
            gridRef.current.api.refreshServerSide();
        }
    };
    useEffect(() => {
        const detailClassification = classificationSchema[selectedClassification];
        if (detailClassification) {
            applicableEntityTypes.current = detailClassification.applicableEntityTypes;
            selectedRef.current = detailClassification.classification.name;
        }
    }, [classificationSchema[selectedClassification]]);

    const gridOptions: GridOptions = useMemo(
        () => ({
            loadingOverlayComponent: Loading,
            noRowsOverlayComponent: NoRowsOverlay,
            noRowsOverlayComponentParams: {
                search: searchQuery,
                initialSearchStr: 2,
            },
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                autoHeight: true,
                wrapText: true,
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'properties.name',
                    headerName: 'Name',
                    minWidth: 280,
                    checkboxSelection: true,
                    rowDrag: true,
                    filterParams,
                },
                {
                    field: 'properties.type',
                    headerName: 'Type',
                    minWidth: 140,
                    valueGetter: (props) => {
                        if (props.context.schemaTreeMap) {
                            return (
                                props.context.schemaTreeMap?.[props.data?.properties?.type]?.displayName ||
                                props.data?.properties?.type
                            );
                        }
                        return props.data?.type;
                    },
                    filterParams,
                },
                {
                    field: 'properties.revision',
                    headerName: 'Revision',
                    minWidth: 90,
                    filterParams,
                },
            ],
            components: {},
            cacheBlockSize: 50,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            rowSelection: 'multiple',
            suppressRowClickSelection: true,
        }),
        [gridRef]
    );

    const buildSearchQuery = useCallback(
        (search, types) => {
            const searchText = search || '';
            return buildAndOperatorQuery([
                buildOrOperatorQuery([
                    buildContainsQuery('name', searchText),
                    buildContainsQuery('description', searchText),
                ]),
                buildInQuery('type', types),
                buildExactQuery('permissions', 'canClassify'),
            ]);
        },
        [classificationSchema]
    );

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow,
                limit: 50,
                ...buildSortParams(params.sortModel),
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                filterConditions.push(buildSearchQuery(searchQuery.current, applicableEntityTypes.current));
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else if (searchQuery.current) {
                queryParams['query'] = JSON.stringify(
                    buildSearchQuery(searchQuery.current, applicableEntityTypes.current)
                );
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                if (isNil(searchQuery.current) || searchQuery.current.length < 2) {
                    params.success({ rowData: [], rowCount: 0 });
                    return;
                }

                apiFetch({
                    ...entityUrls.getListEntitySysRoot,
                    qs: buildParams(params?.request),
                })
                    .then((response: any) => {
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .catch(() => {
                        params.failCallback();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    const addDropZone = (params) => {
        const container = document.querySelector('#catalog-table');
        const dropZone = {
            getContainer: () => container,
            onDragStop: (params) => {
                handleSubmit();
            },
        };
        params.api.addRowDropZone(dropZone);
    };

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource();
        event.api.setServerSideDatasource(dataSource);
        addDropZone(event);
    }, []);

    const isValidSearch = Boolean(search && search.length >= 2);

    return (
        <RightTray
            title="Classify Entity"
            open={open}
            onClose={handleClose}
            onSearchChange={handleSearchChanged}
            onConfirm={handleSubmit}
            confirmText="Apply"
            searchMsg={isValidSearch ? null : 'Enter at least 2 characters to search'}
            enableSearch
            componentName={DRAWER_COMPONENT_NAME.CLASSIFY_ENTITY_CATALOG}
        >
            {isLoadingClassificationSchema ? (
                <Loading />
            ) : (
                <Box
                    sx={{
                        color: '#FFFFFF',
                        height: '100%',
                        ...tableStyles,
                        '& .ag-header': { border: 'none' },
                        '& .ag-root-wrapper': {
                            border: 'none !important',
                        },
                        margin: '16px 24px',
                        mt: 0,
                    }}
                >
                    <Grow in={isValidSearch}>
                        <div style={{ height: '100%', overflow: 'auto' }}>
                            <AgGridReact
                                className="ag-theme-alpine"
                                ref={gridRef}
                                {...gridOptions}
                                onGridReady={handleSetDataSource}
                                onRowDragEnter={(e) => e.node.setSelected(true)}
                                icons={tableIcons}
                                context={{ schemaTreeMap: schemaTreeMap }}
                            />
                        </div>
                    </Grow>
                </Box>
            )}
        </RightTray>
    );
};

export default AddEntity;
