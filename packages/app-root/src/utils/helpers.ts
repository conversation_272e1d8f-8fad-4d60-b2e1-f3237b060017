/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';

const ADMIN_ROLES = ['admin', 'superadmin'];

export const DEFAULT_KC_CLIENT_ID = 'sp-services';

export const getTenant = () => {
    let tenant = window.location.hostname.split('.')[0];
    tenant =
        tenant === process.env.MASTER_DOMAIN
            ? process.env.DEFAULT_TENANT_ID
            : new RegExp(process.env.DOMAIN_PATTERN).exec(tenant)[1];
    return tenant;
};

export const isAdmin = (keycloak) => {
    const roles = get(keycloak, 'realmAccess.roles', []);
    return roles.some((role: string) => ADMIN_ROLES.includes(role.toLowerCase()));
};

export const authorized = (location) => {
    return !location.pathname.startsWith('/access-denied');
};

export const parseToken = (token: string) => {
    try {
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) return null;

        const payload = JSON.parse(atob(tokenParts[1]));
        return payload;
    } catch (err) {
        console.error('Failed to parse token:', err);
        return null;
    }
};

export const isTokenValid = (token: string) => {
    const payload = parseToken(token);
    if (!payload || !payload.exp) return false;

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp > currentTime + 30; // 30 seconds buffer
};

const MAX_TIMEOUT = 2147483647;
export const scheduleTokenExpiryAction = (token: string, onExpire: () => void) => {
    const payload = parseToken(token);
    if (!payload?.exp) return;

    const expiryTimeInMs = payload.exp * 1000;
    const currentTimeInMs = Date.now();

    const timeoutDuration = expiryTimeInMs - currentTimeInMs;
    console.log('Session expires in', timeoutDuration, 'ms');

    // setTimeout has a maximum delay limit of ~24.8 days.
    // Ensure timeout does not exceed this limit to avoid unexpected behavior.
    setTimeout(() => {
        onExpire();
    }, Math.max(0, Math.min(MAX_TIMEOUT, timeoutDuration)));
};
