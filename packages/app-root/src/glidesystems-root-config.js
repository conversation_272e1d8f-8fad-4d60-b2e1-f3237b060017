/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { constructRoutes, constructApplications, constructLayoutEngine } from 'single-spa-layout';
import { registerApplication, start } from 'single-spa';

import KeyCloak from 'keycloak-js';
import { isAdmin, getTenant, DEFAULT_KC_CLIENT_ID, isTokenValid, scheduleTokenExpiryAction } from './utils/helpers';

const tenant = getTenant();

const keycloak = new KeyCloak({
    url: `${process.env.IDENTITY_SERVICE_URL}/auth`,
    realm: tenant,
    clientId: DEFAULT_KC_CLIENT_ID,
});

const routes = constructRoutes(document.querySelector('#single-spa-layout'), {
    loaders: {
        main: `
    <div id="loading-container">
      <div class="loader"></div>
    </div>
    `,
    },
    errors: {
        topNav: '<h1>Failed to load application</h1>',
    },
    props: {
        authentication: {
            keycloak,
            isAdmin: isAdmin(keycloak),
        },
    },
});

const applications = constructApplications({
    routes,
    loadApp: ({ name }) => System.import(name),
});

const layoutEngine = constructLayoutEngine({
    routes,
    applications,
});

applications.forEach(registerApplication);

const storedToken = window.sessionStorage.getItem('token');
keycloak
    .init({
        checkLoginIframe: false,
        promiseType: 'native',
    })
    .then((authenticated) => {
        if (authenticated) {
            // Store the token after authentication
            window.sessionStorage.setItem('token', keycloak.token);
            window.sessionStorage.setItem('idToken', keycloak.idToken);
            window.localStorage.setItem('tenantId', keycloak.realm || tenant);
            scheduleTokenExpiryAction(keycloak.token, () => {
                keycloak.logout({ logoutMethod: 'POST' }).then(() => {
                    window.sessionStorage.clear();
                });
            });
            layoutEngine.activate();
            start();
        } else if (storedToken && isTokenValid(storedToken)) {
            scheduleTokenExpiryAction(storedToken, () => {
                keycloak.logout({ logoutMethod: 'POST' }).then(() => {
                    window.sessionStorage.clear();
                });
            });
            layoutEngine.activate();
            start();
        } else {
            console.warn('User is not authenticated');
            keycloak.login();
        }
    })
    .catch((error) => {
        console.error('Failed to authenticate user', error);
    })
    .finally(() => {
        // Always remove the loading spinner after initialization
        const loadingElement = document.querySelector('#initial-loading');
        if (loadingElement) {
            loadingElement.parentNode.removeChild(loadingElement);
        }
    });
