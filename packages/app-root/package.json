{"name": "@glidesystems/root-config", "version": "1.0.0", "scripts": {"start": "rspack serve --port 9000 --env isLocal", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "build": "concurrently pnpm:build:*", "build:rspack": "rspack --mode=production", "clean": "rm -rf node_modules"}, "devDependencies": {"@rspack/core": "^1.2.3", "@rspack/cli": "^1.2.3", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-ts-important-stuff": "^1.1.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "serve": "^12.0.0", "ts-config-single-spa": "^3.0.0", "typescript": "5.7.3", "webpack-config-single-spa-ts": "^4.0.0", "webpack-merge": "^5.8.0", "@types/lodash": "^4.17.20", "@types/node": "^24.2.0", "@babel/core": "^7.0.0"}, "dependencies": {"@types/jest": "^27.0.1", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/systemjs": "^6.1.1", "classnames": "^2.3.1", "lodash": "^4.17.21", "dotenv": "^11.0.0", "keycloak-js": "^26.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "react-router-dom": "^5.2.0", "single-spa": "^5.9.3", "single-spa-layout": "^2.0.1", "tinycolor2": "^1.4.2"}, "types": "dist/glidesystems-root-config.d.ts", "browser": {"fs": false, "path": false, "os": false}}