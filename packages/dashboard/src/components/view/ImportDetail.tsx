/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    DRAWER_COMPONENT_NAME,
    ResizableDrawer,
    CloseIcon,
    formatDateTime,
    Loading,
    tableStyles,
} from '@glidesystems/styleguide';
import { useStore } from '../../store';
import { Box, Chip, Grid, IconButton, Typography } from '@mui/material';
import { IMPORT_STATUS_VARIANT, INFINITE_BATCH_SIZE } from '../../constant';
import { useCallback, useMemo, useRef } from 'react';
import { migrationUrls, fetch } from '@glidesystems/api';
import { AgGridReact } from '@ag-grid-community/react';

const Attribute = ({ label, value, fullWidth = false }) => {
    return (
        <Grid item {...(fullWidth ? { xs: 12, md: 12 } : { xs: 12, md: 6 })}>
            <Typography
                variant="label3-med"
                sx={{
                    display: 'block',
                    color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                }}
            >
                {label}
            </Typography>
            <Typography
                variant="bo1"
                sx={{
                    display: 'block',
                    color: (theme) => theme.palette.glide.text.normal.inverseTertiary,
                    mt: '4px',
                }}
            >
                {value}
            </Typography>
        </Grid>
    );
};
const ImportDetail = () => {
    const [selectedImport, setSelectedImport] = useStore((state) => [state.selectedImport, state.setSelectedImport]);
    const open = Boolean(selectedImport);
    const onClose = () => setSelectedImport(null);
    const gridRef = useRef<AgGridReact>();

    const createServerSideDataSource = useCallback(() => {
        return {
            getRows: (params: any) => {
                params.startRow === 0 && gridRef.current.api.showLoadingOverlay();
                fetch({
                    ...migrationUrls.getFailedImport,
                    params: {
                        id: selectedImport?.id,
                    },
                    qs: {
                        limit: INFINITE_BATCH_SIZE,
                        offset: params.startRow,
                    },
                })
                    .then(({ data: { data, pageInfo } }) => {
                        params.success({
                            rowData: data,
                            rowCount: pageInfo.total || data.length,
                        });
                    })
                    .catch(() => {
                        params.failCallback();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, [selectedImport]);

    const gridOptions: any = useMemo(
        () => ({
            rowHeight: 34,
            headerHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: false,
                resizable: true,
                filter: false,
                autoHeight: true,
                wrapText: true,
            },
            columnDefs: [
                {
                    field: 'lineNumber',
                    headerName: 'Row',
                    flex: 1,
                },
                {
                    field: 'responseBody.error_msg',
                    headerName: 'Error Message',
                    flex: 3,
                },
            ],
            cacheBlockSize: 20,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
        }),
        []
    );

    const handleSetDataSource = useCallback(
        (event) => {
            const dataSource = createServerSideDataSource();
            gridOptions.dataSource = dataSource;
            event.api.setServerSideDatasource(dataSource);
        },
        [gridOptions, createServerSideDataSource]
    );

    return (
        <ResizableDrawer
            open={open}
            onClose={onClose}
            componentName={DRAWER_COMPONENT_NAME.IMPORT_PROGRESS_DETAIL}
            defaultWidth={475}
            minWidth={475}
        >
            <Box
                sx={{
                    display: 'flex',
                    color: '#545454',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: '24px',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                    mb: 'auto',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        gap: '16px',
                        position: 'relative',
                    }}
                >
                    <Typography
                        sx={{
                            fontSize: '20px',
                            lineHeight: '150%',
                            fontWeight: 600,
                            cursor: 'default',
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                        }}
                    >
                        Import Detail
                    </Typography>
                    <IconButton
                        onClick={onClose}
                        sx={{
                            position: 'absolute',
                            right: 0,
                            height: '24px',
                            width: '24px',
                            p: 0,
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Box>
            </Box>
            <Box
                sx={{
                    display: 'flex',
                    padding: '8px 16px',
                    gap: '16px',
                    flexDirection: 'column',
                    height: '100%',
                    overflow: 'auto',
                }}
            >
                <Typography variant="ol1" color="info">
                    General Info
                </Typography>
                <Grid container spacing={1}>
                    <Attribute fullWidth label="File Name" value={selectedImport?.originalFileName} />
                    <Attribute
                        label="Status"
                        value={
                            <Chip
                                size="small"
                                label={selectedImport?.fileImportStatus}
                                variant={IMPORT_STATUS_VARIANT[selectedImport?.fileImportStatus]}
                            />
                        }
                    />
                    <Attribute label="Requested By" value={selectedImport?.requestedBy} />
                    <Attribute label="Created Date" value={formatDateTime(selectedImport?.createdAt)} />
                    <Attribute label="Updated Date" value={formatDateTime(selectedImport?.updatedAt)} />
                    <Attribute label="Processed rows" value={selectedImport?.processedRows} />
                    <Attribute fullWidth label="Error Reason" value={selectedImport?.errorReason} />
                </Grid>
                <Typography variant="ol1" color="info">
                    Error Details
                </Typography>
                <Box
                    sx={{
                        height: '100%',
                        width: '100%',
                        ...tableStyles,
                        minHeight: '400px',
                    }}
                >
                    <AgGridReact
                        className="ag-theme-alpine"
                        ref={gridRef}
                        {...gridOptions}
                        onGridReady={handleSetDataSource}
                    />
                </Box>
            </Box>
        </ResizableDrawer>
    );
};

export default ImportDetail;
