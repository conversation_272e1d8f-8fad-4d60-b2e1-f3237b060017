/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SvgIcon } from '@mui/material';

export const TimeIcon = (props) => (
    <SvgIcon {...props} style={{ width: 12, height: 12 }} viewBox="0 0 12 12" fill="none">
        <path
            d="M6 11.25C4.96165 11.25 3.94662 10.9421 3.08326 10.3652C2.2199 9.78834 1.54699 8.9684 1.14963 8.00909C0.752275 7.04978 0.648307 5.99418 0.85088 4.97578C1.05345 3.95738 1.55347 3.02192 2.28769 2.28769C3.02192 1.55347 3.95738 1.05345 4.97578 0.85088C5.99418 0.648307 7.04978 0.752275 8.00909 1.14963C8.9684 1.54699 9.78834 2.2199 10.3652 3.08326C10.9421 3.94662 11.25 4.96165 11.25 6C11.25 7.39239 10.6969 8.72775 9.71231 9.71231C8.72775 10.6969 7.39239 11.25 6 11.25ZM6 1.5C5.10999 1.5 4.23996 1.76392 3.49994 2.25839C2.75992 2.75286 2.18314 3.45566 1.84254 4.27793C1.50195 5.10019 1.41284 6.00499 1.58647 6.87791C1.7601 7.75082 2.18869 8.55265 2.81802 9.18198C3.44736 9.81132 4.24918 10.2399 5.1221 10.4135C5.99501 10.5872 6.89981 10.4981 7.72208 10.1575C8.54434 9.81687 9.24715 9.24009 9.74162 8.50007C10.2361 7.76005 10.5 6.89002 10.5 6C10.5 4.80653 10.0259 3.66194 9.18198 2.81802C8.33807 1.97411 7.19348 1.5 6 1.5Z"
            fill="inherit"
        />
        <path d="M7.72125 8.25L5.625 6.15375V2.625H6.375V5.8425L8.25 7.72125L7.72125 8.25Z" fill="inherit" />
    </SvgIcon>
);

export const TypeIcon = (props) => (
    <SvgIcon {...props} style={{ width: 12, height: 12 }} viewBox="0 0 12 12" fill="none">
        <path d="M5.25 6.75H3V7.5H5.25V6.75Z" fill="inherit" />
        <path d="M6.75 8.25H3V9H6.75V8.25Z" fill="inherit" />
        <path
            d="M9.75 1.5H2.25C2.05116 1.50025 1.86054 1.57935 1.71994 1.71994C1.57935 1.86054 1.50025 2.05116 1.5 2.25V9.75C1.50025 9.94884 1.57935 10.1395 1.71994 10.2801C1.86054 10.4207 2.05116 10.4998 2.25 10.5H9.75C9.94884 10.4998 10.1395 10.4207 10.2801 10.2801C10.4207 10.1395 10.4998 9.94884 10.5 9.75V2.25C10.4998 2.05116 10.4207 1.86054 10.2801 1.71994C10.1395 1.57935 9.94884 1.50025 9.75 1.5ZM6.75 2.25V3.75H5.25V2.25H6.75ZM2.25 9.75V2.25H4.5V4.5H7.5V2.25H9.75L9.75045 9.75H2.25Z"
            fill="inherit"
        />
    </SvgIcon>
);
