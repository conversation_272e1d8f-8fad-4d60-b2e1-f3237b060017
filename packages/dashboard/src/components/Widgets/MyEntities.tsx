/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { memo, useEffect, useRef, useCallback, useMemo } from 'react';
import { Chip, styled, Typography, IconButton, Button } from '@mui/material';
import {
    entityUrls,
    fetch as apiFetch,
    assetServiceUrl,
    SYSTEM_ENTITY_TYPE,
    getThumbnailUrl,
    buildInQuery,
    downloadFile,
    buildAndOperatorQuery,
    buildExactQuery,
    SYSTEM_RELATION,
} from '@glidesystems/api';
import {
    DownloadIcon,
    LockIcon,
    DocThumbnail,
    formatDateTime,
    tableStyles,
    Loading,
    EmptyDocument,
    Thumbnail,
    PlusIcon,
} from '@glidesystems/styleguide';
import get from 'lodash/get';
import WidgetItemTitle from '../Base/WidgetItemTitle';
import { TimeIcon } from '../icons';
import isEmpty from 'lodash/isEmpty';
import WidgetField from '../Base/WidgetField';
import { getStatusVariant } from '../../utils/common';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import { INFINITE_BATCH_SIZE } from '../../constant';
import { useStore } from '../../store';
import { AgGridReact } from '@ag-grid-community/react';
import {
    getUserIdFromUserInfo,
    SchemaTreeMapEntry,
    useAuth,
    useCreateEntity,
    useSchemaTree,
} from '@glidesystems/caching-store';

const ItemWrapper = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    minHeight: '36px',
    gap: '8px',
    transition: 'ease 0.2s',
    cursor: 'pointer',
    textDecoration: 'none',
    width: '100%',
    position: 'relative',
    '& .info': {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'space-between',
        width: '100%',
        gap: '4px',
    },
    '& .status': {
        display: 'flex',
        gap: '12.75px',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
    },
    '& .author': {
        fontSize: '10px',
        color: theme.palette.glide.text.normal.inversePrimary,
        letterSpacing: '0.005em',
    },
    '& .text': {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
    },
    '& .description': {
        color: theme.palette.glide.text.tertiary,
        fontWeight: 500,
        fontSize: '10px',
        maxWidth: '50%',
    },
}));

const Item = ({ data, onDownload, canDownload }) => {
    if (!data) {
        return '';
    }

    const {
        id,
        properties: { name, type, primaryFileExtension, primaryFileId, title, hasThumbnail, revision, isMaster },
        permissions: { canCheckOut = false },
        schemaType,
    } = data;
    const state = data.state;
    const itemName = `${name}${primaryFileExtension ? '.' + primaryFileExtension : ''}`;
    const locked = data.lockedBy;
    const hasFile = Boolean(primaryFileId);

    const isDocument = schemaType.some((type) =>
        [SYSTEM_ENTITY_TYPE.DOCUMENT, SYSTEM_ENTITY_TYPE.DOCUMENT_MASTER].includes(type)
    );

    return (
        //@ts-ignore
        <ItemWrapper component={Link} to={`/detail/${type}/${id}/properties`}>
            {isDocument ? (
                <DocThumbnail type={primaryFileExtension} alt={itemName} />
            ) : (
                <Thumbnail
                    hasThumbnail={hasThumbnail}
                    size={{ width: 32, height: 32 }}
                    url={getThumbnailUrl(type, id)}
                />
            )}
            <div className="info">
                <WidgetItemTitle>
                    {itemName}
                    {revision && (
                        <>
                            {` - `}
                            <Typography
                                sx={{
                                    fontSize: '14px',
                                    ml: '2px',
                                    fontWeight: 500,
                                    color: (theme) => theme.palette.success.main,
                                    display: 'inline',
                                }}
                            >
                                {revision}
                            </Typography>
                        </>
                    )}
                    {isMaster && (
                        <>
                            {` - `}
                            <Typography
                                sx={{
                                    fontSize: '14px',
                                    ml: '2px',
                                    fontWeight: 500,
                                    color: (theme) => theme.palette.info.main,
                                    display: 'inline',
                                }}
                            >
                                Master
                            </Typography>
                        </>
                    )}
                </WidgetItemTitle>
                <WidgetField icon={<TimeIcon />} value={formatDateTime(data.updatedAt)} />
                <div style={{ display: 'flex', gap: '8px' }}>
                    {title && <Typography className="text description">{title}</Typography>}
                    {!isEmpty(locked) && (
                        <WidgetField
                            icon={<LockIcon style={{ width: 12, height: 12 }} />}
                            value={
                                <>
                                    Locked by <b>{locked.name}</b>
                                </>
                            }
                        />
                    )}
                </div>
            </div>
            <div className="status">
                <Chip size="small" label={state.name} variant={getStatusVariant(state)} />
                {canDownload && (
                    <span>
                        <IconButton
                            size="small"
                            onClick={(e) => {
                                e.preventDefault();
                                onDownload(data);
                            }}
                            disabled={!(canCheckOut && hasFile)}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </span>
                )}
            </div>
        </ItemWrapper>
    );
};

const NoRowsOverlay = ({ entityType, title }: { entityType: string; title: string }) => {
    const { setSelectedEntityType } = useCreateEntity();
    const { schemaTreeMap } = useSchemaTree();
    return (
        <div style={{ transform: 'translateY(-15%)', marginTop: '12px' }}>
            <EmptyDocument />
            <Typography
                sx={{
                    fontSize: '16px',
                    color: (theme) => theme.palette.glide.text.normal.main,
                    fontWeight: 500,
                    textAlign: 'center',
                    mt: '10px',
                }}
            >
                {`No data for ${title} found.`}
            </Typography>
            <Button
                sx={{
                    minWidth: '32px',
                    mt: '6px',
                    pointerEvents: 'all',
                }}
                size="small"
                variant="contained-blue"
                endIcon={
                    <PlusIcon
                        sx={{
                            width: 16,
                            height: 16,
                        }}
                    />
                }
                onClick={() => {
                    setSelectedEntityType(schemaTreeMap?.[entityType]);
                }}
            >
                {`Add ${title}`}
            </Button>
        </div>
    );
};

const MyEntities = ({ canDownload = false, entityTypes, title }) => {
    const userInfo = useAuth((state) => state.userInfo);
    const gridRef = useRef<AgGridReact>();
    const { widgets } = useStore();

    useEffect(() => {
        // To avoid blank page when scroll offset is reset when widget is dropped but not updating to the library
        gridRef.current?.api?.ensureIndexVisible(0);
    }, [widgets]);

    const downloadDocumentFile = async (document) => {
        gridRef.current.api.showLoadingOverlay();
        try {
            const { id, primaryFileExtension: extensions, primaryFileId: fileId } = document;
            const queryParams = {
                fileId,
                extensions,
            };
            const { data } = await apiFetch({ ...assetServiceUrl.documentCheckout, params: { id }, qs: queryParams });
            const files = Object.values(data.fileResponses);
            const downloadUrl = get(files, ['0', 'downloadUrl']);
            const name = get(files, ['0', 'properties', 'name'], 'File');
            downloadFile(downloadUrl, name);
        } catch (error) {
            console.error(error);
        } finally {
            gridRef.current.api.hideOverlay();
        }
    };

    const createServerSideDataSource = useCallback(() => {
        return {
            getRows: (params: any) => {
                const userId = getUserIdFromUserInfo(userInfo);
                params.startRow === 0 && gridRef.current.api.showLoadingOverlay();
                apiFetch({
                    ...entityUrls.getListEntity,
                    params: { entityType: entityTypes.length > 1 ? SYSTEM_ENTITY_TYPE.SYS_ROOT : entityTypes[0] },
                    qs: {
                        limit: INFINITE_BATCH_SIZE,
                        offset: params.startRow,
                        sort: 'createdAt,DESCENDING',
                        query:
                            entityTypes.length > 1
                                ? JSON.stringify(
                                      buildAndOperatorQuery([
                                          buildInQuery('schemaType', entityTypes),
                                          buildExactQuery(`relation.${SYSTEM_RELATION.AUTHORED_BY}.id`, userId),
                                      ])
                                  )
                                : JSON.stringify(buildExactQuery(`relation.${SYSTEM_RELATION.AUTHORED_BY}.id`, userId)),
                        fields: ['revision'],
                    },
                })
                    .then(({ data: { data, pageInfo } }) => {
                        const lastRow = params.endRow >= pageInfo.total ? pageInfo.total : -1;

                        params.successCallback(data, lastRow);
                    })
                    .catch(() => {
                        params.failCallback();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, [userInfo]);

    const gridOptions: any = useMemo(() => {
        return {
            rowHeight: 62,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: false,
                resizable: false,
                flex: 1,
                filter: false,
                cellStyle: {
                    display: 'flex',
                },
            },
            getRowId: (params) => params.data.id,
            columnDefs: [
                {
                    field: 'doc',
                    headerName: 'Entity',
                    flex: 1,
                    cellRenderer: Item,
                    cellRendererParams: {
                        onDownload: downloadDocumentFile,
                        canDownload: canDownload,
                    },
                },
            ],
            cacheBlockSize: 20,
            rowModelType: 'infinite',
            rowStyle: {
                border: 'none',
            },
        };
    }, []);

    const handleSetDataSource = useCallback(
        (event) => {
            const dataSource = createServerSideDataSource();
            gridOptions.dataSource = dataSource;
            event.api.setDatasource(dataSource);
        },
        [userInfo, gridOptions]
    );

    return (
        <div className="scrollableContainer">
            <div className="content" style={{ height: '100%', width: '100%' }}>
                {userInfo && (
                    <Box
                        sx={{
                            height: '100%',
                            width: '100%',
                            '& .ag-header': {
                                display: 'none',
                            },
                            ...tableStyles,
                        }}
                    >
                        <AgGridReact
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            onGridReady={handleSetDataSource}
                            noRowsOverlayComponent={NoRowsOverlay}
                            noRowsOverlayComponentParams={{
                                title: title,
                                entityType: entityTypes.length > 1 ? SYSTEM_ENTITY_TYPE.SYS_ROOT : entityTypes[0],
                            }}
                        />
                    </Box>
                )}
            </div>
        </div>
    );
};

export default memo(MyEntities);
