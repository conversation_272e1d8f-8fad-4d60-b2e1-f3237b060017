/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Typography } from '@mui/material';

const WidgetField = ({ icon = null, value, textClassName = '' }) => {
    return (
        value && (
            <Typography
                sx={{
                    fontSize: '10px',
                    fontWeight: 400,
                    lineHeight: '135%',
                    color: (theme) => theme.palette.glide.primary,
                    display: 'flex',
                    alignItems: 'center',
                    '& .overdue': {
                        color: (theme) => theme.palette.error.main,
                    },
                    '& .warning': {
                        color: (theme) => theme.palette.warning.main,
                    },
                }}
            >
                {icon}
                <span
                    className={textClassName}
                    style={{
                        marginLeft: icon ? '4.75px' : 0,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    }}
                >
                    {value}
                </span>
            </Typography>
        )
    );
};

export default WidgetField;
