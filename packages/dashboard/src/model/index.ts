/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export type WidgetItem = {
    id: string;
    data: {
        title: string;
        fullWidth?: boolean;
        entityType?: string;
    };
};

export interface Task {
    id: string;
    name: string;
    description: string;
    assignee: string;
    created: string;
    due: string;
    followUp: string;
    delegationState: string;
    executionId: string;
    owner: string;
    priority: number;
    processDefinitionId: string;
    processInstanceId: string;
    taskDefinitionKey: string;
    caseExecutionId: string;
    caseInstanceId: string;
    caseDefinitionId: string;
    suspended: boolean;
    formKey: string;
    tenantId: string;
}

export interface Document {
    id: string;
    updatedAt: string;
    createdAt: string;
    createdBy: string;
    extension: string | null;
    parentFolderId: string;
    isLocked: boolean;
    name: string;
    description: string;
    fileLocation: string;
    type: string;
    primaryFileId: string;
    primaryFileExtension: string;
    revision: string;
    permissions: Record<string, string>;
    state: {
        name: string;
        isSuperseded: boolean;
        isObsoleted: boolean;
        isOfficial: boolean;
    };
    lifeCycle: {
        name: string;
        id: string;
    };
    owner: {
        createdAt: string;
        name: string;
        type: string;
        updatedAt: string;
    };
    lockedBy: Record<string, string>;
    classifications: string[] | null;
}

export interface Dashboard {
    widgets: WidgetItem[];
    selectedImport?: any | null;
    setSelectedImport: (importJob: any) => void;
    setWidgets: (data: WidgetItem[]) => void;
    deleteWidget: (id: string) => void;
}
export type PageInfo = {
    total: number;
    limit?: number;
    nextOffset?: number | null;
};

export type ProcessTaskResponse = {
    data: Task[];
    pageInfo: PageInfo;
};
