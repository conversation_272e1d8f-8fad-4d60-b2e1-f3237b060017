/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { DASHBOARD_STORAGE_KEY, DEFAULT_WIDGETS } from '../constant';
import { Dashboard } from '../model';

export const useStore = create<Dashboard>()(
    devtools(
        persist(
            (set, get) => ({
                widgets: DEFAULT_WIDGETS,
                selectedImport: null,
                failedImport: null,
                setSelectedImport: (selectedImport) => set({ selectedImport }),
                setWidgets: (data) => set({ widgets: data }),
                deleteWidget: (id: string) => {
                    set({ widgets: [...get().widgets].filter((widget) => widget.id !== id) });
                },
            }),
            {
                name: DASHBOARD_STORAGE_KEY,
                version: 3,
                migrate: (persistedState: any, version) => {
                    if (version < 3) {
                        persistedState = {
                            ...persistedState,
                            widgets: DEFAULT_WIDGETS,
                        };
                    }

                    return persistedState;
                },
                partialize: (state) =>
                    Object.fromEntries(
                        Object.entries(state).filter(([key]) => !['selectedImport', 'failedImport'].includes(key))
                    ),
            }
        )
    )
);
