/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { WidgetItem } from '../model';

const DASHBOARD_STORAGE_KEY = 'dashboardStorage';

export const PREBUILT_WIDGET = [
    {
        id: 'my-tasks',
        data: { title: 'My Tasks' },
    },
    {
        id: 'recently-viewed',
        data: { title: 'Recently Viewed' },
    },
    {
        id: 'import-progress',
        data: { title: 'Import Progress' },
    },
];
const DEFAULT_WIDGETS: WidgetItem[] = [
    ...PREBUILT_WIDGET,
    {
        id: 'my-entities-Document',
        data: {
            title: 'Document',
            entityType: 'Document',
        },
    },
    {
        id: 'my-entities-Issue',
        data: {
            title: 'Issue',
            entityType: 'Issue',
        },
    },
    {
        id: 'my-entities-ChangeRequest',
        data: {
            title: 'Change Request',
            entityType: 'ChangeRequest',
        },
    },
    {
        id: 'my-entities-ChangeOrder',
        data: {
            title: 'Change Order',
            entityType: 'ChangeOrder',
        },
    },
];

const INFINITE_BATCH_SIZE = 20;

const IMPORT_STATUS_VARIANT = {
    COMPLETED: 'status-success',
    ERROR: 'status-error',
    PENDING: 'status-initial',
    PROCESSING: 'status-info',
    VALIDATED: 'status-info',
    CANCELLED: 'status-warning',
    undefined: 'status-initial',
};

export { DASHBOARD_STORAGE_KEY, DEFAULT_WIDGETS, INFINITE_BATCH_SIZE, IMPORT_STATUS_VARIANT };
