/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { StyledEngineProvider, ThemeProvider } from '@mui/material';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Themes, Notfound } from '@glidesystems/styleguide';

import Main from './Main';
import { ModuleRegistry } from '@ag-grid-community/core';
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model';
import { LicenseManager } from '@ag-grid-enterprise/core';
import '@ag-grid-community/core/dist/styles/ag-grid.css';
import '@ag-grid-community/core/dist/styles/ag-theme-alpine.css';
import ImportDetail from './components/view/ImportDetail';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';

LicenseManager.setLicenseKey(process.env.AG_GRID_LICENSE);

ModuleRegistry.registerModules([InfiniteRowModelModule, ServerSideRowModelModule]);

export default function Root(props) {
    return (
        <StyledEngineProvider injectFirst>
            <ThemeProvider theme={Themes.default}>
                <ImportDetail />
                <BrowserRouter>
                    <Routes>
                        <Route path="/dashboard" element={<Main />}></Route>
                        <Route path="*" element={<Notfound />} />
                    </Routes>
                </BrowserRouter>
            </ThemeProvider>
        </StyledEngineProvider>
    );
}
