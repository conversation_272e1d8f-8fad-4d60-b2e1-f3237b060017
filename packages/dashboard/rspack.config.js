const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const { rspack } = require('@rspack/core');
const { merge } = require('webpack-merge');
const dotenv = require('dotenv');
dotenv.config();

module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'glidesystems',
        projectName: 'dashboard',
        webpackConfigEnv,
        argv,
    });
    const isDevelopment = process.env.SOURCE_MAP || false;
    return merge(defaultConfig, {
        module: {
            rules: [],
        },
        plugins: [
            new rspack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
        ],
        devtool: isDevelopment,
        devServer: {
            client: {
                overlay: {
                    errors: true,
                    warnings: false,
                    runtimeErrors: false,
                },
            },
        },
    });
};
