/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
const SearchIcon = (props: Record<string, any>) => {
    return (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M17.375 17.375L13.4497 13.4428L17.375 17.375ZM15.625 8.1875C15.625 10.16 14.8414 12.0518 13.4466 13.4466C12.0518 14.8414 10.16 15.625 8.1875 15.625C6.21495 15.625 4.3232 14.8414 2.92839 13.4466C1.53359 12.0518 0.75 10.16 0.75 8.1875C0.75 6.21495 1.53359 4.3232 2.92839 2.92839C4.3232 1.53359 6.21495 0.75 8.1875 0.75C10.16 0.75 12.0518 1.53359 13.4466 2.92839C14.8414 4.3232 15.625 6.21495 15.625 8.1875V8.1875Z"
                stroke="white"
                strokeLinecap="round"
            />
        </svg>
    );
};

export default SearchIcon;
