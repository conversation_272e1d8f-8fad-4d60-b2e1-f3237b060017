/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';

export const RecentIcon = (props: Record<string, any>) => {
    return (
        <SvgIcon fontSize="inherit" style={{ width: 18, height: 18 }} viewBox="0 0 18 19" {...props}>
            <path
                d="M1.68734 0L1.78859 0.00899999C1.90098 0.0295329 2.00445 0.0838037 2.08524 0.164591C2.16603 0.245378 2.2203 0.348851 2.24083 0.461242L2.24983 0.56249V3.04982C3.59602 1.52293 5.42008 0.497513 7.4242 0.140969C9.42832 -0.215574 11.4941 0.117821 13.2843 1.08672C15.0745 2.05563 16.4834 3.60281 17.2809 5.47566C18.0784 7.34852 18.2175 9.43642 17.6754 11.3985C17.1333 13.3606 15.942 15.0809 14.2961 16.2787C12.6502 17.4764 10.6468 18.0808 8.61314 17.9932C6.57944 17.9055 4.63551 17.1311 3.09872 15.7962C1.56194 14.4613 0.523076 12.6449 0.151743 10.6434C0.0315193 9.90737 -0.0174562 9.16143 0.00549571 8.41597C0.0184136 8.27188 0.0863524 8.13831 0.195196 8.043C0.304039 7.9477 0.445416 7.898 0.589952 7.90423C0.734487 7.91046 0.871066 7.97214 0.971308 8.07645C1.07155 8.18076 1.12775 8.31968 1.12823 8.46435C1.10891 9.12503 1.15185 9.78606 1.25647 10.4387C1.58814 12.2278 2.52913 13.8468 3.91949 15.0206C5.30985 16.1943 7.06379 16.8504 8.88316 16.8773C10.7025 16.9041 12.4751 16.3001 13.8995 15.1679C15.3239 14.0357 16.3123 12.4452 16.6966 10.6666C17.0809 8.88811 16.8375 7.03138 16.0077 5.41203C15.1779 3.79269 13.8129 2.51067 12.1448 1.78388C10.4767 1.0571 8.60836 0.930409 6.8574 1.42534C5.10643 1.92028 3.58089 3.00629 2.54007 4.49879H5.62252L5.72377 4.50892C5.85346 4.5324 5.97078 4.60068 6.05527 4.70184C6.13976 4.803 6.18604 4.93061 6.18604 5.06241C6.18604 5.19421 6.13976 5.32182 6.05527 5.42298C5.97078 5.52414 5.85346 5.59242 5.72377 5.6159L5.62252 5.6249H1.68734L1.58609 5.6159C1.47355 5.59529 1.36996 5.54086 1.28916 5.45985C1.20836 5.37885 1.15418 5.27513 1.13385 5.16253L1.12485 5.06128V0.561365L1.13385 0.460117C1.15459 0.347932 1.20895 0.244705 1.28972 0.164135C1.37049 0.0835648 1.47386 0.0294599 1.58609 0.00899999L1.68734 0ZM8.43722 4.49992L8.53847 4.50892C8.65086 4.52945 8.75433 4.58372 8.83512 4.66451C8.9159 4.7453 8.97017 4.84877 8.99071 4.96116L8.99971 5.06241V9.00321H11.2497L11.3509 9.01221C11.4799 9.03631 11.5965 9.10478 11.6804 9.20576C11.7642 9.30674 11.8101 9.43388 11.8101 9.56514C11.8101 9.6964 11.7642 9.82354 11.6804 9.92452C11.5965 10.0255 11.4799 10.094 11.3509 10.1181L11.2497 10.1282H8.43722L8.33597 10.1192C8.22358 10.0987 8.12011 10.0444 8.03932 9.9636C7.95853 9.88281 7.90426 9.77934 7.88373 9.66695L7.87473 9.5657V5.06353L7.88373 4.96229C7.90426 4.8499 7.95853 4.74642 8.03932 4.66563C8.12011 4.58485 8.22358 4.53058 8.33597 4.51004L8.43722 4.50104V4.49992Z"
                fill="white"
            />
        </SvgIcon>
    );
};

export const EnterIcon = (props: Record<string, any>) => {
    return (
        <SvgIcon {...props} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M10.2929 7.20705L12.0859 9H3V4H2V9C2.00032 9.26512 2.10578 9.51929 2.29324 9.70676C2.48071 9.89422 2.73488 9.99968 3 10H12.0859L10.293 11.7929L11 12.5L14 9.5L11 6.5L10.2929 7.20705Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};
export const CollapseIcon = (props: Record<string, any>) => {
    return (
        <SvgIcon width="32" height="32" viewBox="0 0 32 32" fill="inherit" {...props}>
            <path d="M28 8L14 8V10.2857H28V8Z" fill="inherit" />
            <path d="M28 21.7143H14V24H28V21.7143Z" fill="inherit" />
            <path
                d="M10.41 22.4098L11.82 20.9998L7.83 16.9998H28V14.9998H7.83L11.82 10.9998L10.41 9.58984L4 15.9998L10.41 22.4098Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const ExpandIcon = (props: Record<string, any>) => {
    return (
        <SvgIcon width="32" height="32" viewBox="0 0 32 32" fill="inherit" {...props}>
            <path d="M4 24L18 24L18 21.7143L4 21.7143L4 24Z" fill="inherit" />
            <path d="M4 10.2857L18 10.2857L18 8L4 8L4 10.2857Z" fill="inherit" />
            <path
                d="M21.59 9.59015L20.18 11.0002L24.17 15.0002L4 15.0002L4 17.0002L24.17 17.0002L20.18 21.0002L21.59 22.4102L28 16.0002L21.59 9.59015Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};
