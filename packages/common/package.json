{"name": "@glidesystems/common", "version": "1.0.0", "scripts": {"start": "rspack serve --port 9022", "start:standalone": "rspack serve --env standalone", "build": "concurrently pnpm:build:*", "build:rspack": "rspack --mode=production", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage", "clean": "rm -rf node_modules"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "babel-jest": "^27.0.6", "@rspack/core": "^1.2.3", "@rspack/cli": "^1.2.3", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "ts-config-single-spa": "^3.0.0", "typescript": "5.7.3", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@mui/icons-material": "^5.3.1", "@mui/material": "^5.3.1", "@mui/styles": "^5.6.2", "@types/jest": "^27.0.1", "@types/react-dom": "^18.3.0", "@types/systemjs": "^6.1.1", "react": "^18.3.0", "react-dom": "^18.3.0", "react-router-dom": "^6.3.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "@glidesystems/styleguide": "workspace:*"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --no-ignore --fix", "git add --force"], "*.{json,md,js,jsx,ts,tsx}": ["prettier --write", "git add --force"]}, "types": "dist/glidesystems-common.d.ts"}